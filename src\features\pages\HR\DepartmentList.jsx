"use client"

import { useEffect, useState, useRef } from "react"
import { useNavigate } from "react-router-dom"
import { getAllDepartments, deleteDepartment, checkDepartmentPermissions } from '../../../api/departmentManagement';
import DepartmentForm from '../../components/DepartmentForm';
import { showSuccess, showError } from '../../../components/Toastify';
import '../../../styles/ListUser.css';
import '../../../styles/HRLayout.css';
import editIcon from '../../../assets/edit.svg';
import searchIcon from '../../../assets/search.svg';
import addIcon from '../../../assets/add.svg';
import downloadIcon from '../../../assets/download.svg';
import departmentIcon from '../../../assets/icon-sidebar/doingu.svg';
import dropdownIcon from '../../../assets/icon-sidebar/dropdown.svg';
import usersIcon from '../../../assets/users.svg';
import trashIcon from '../../../assets/trash.svg';
import * as XLSX from 'xlsx';

const PAGE_SIZE = 10

const statusOptions = [
  { value: "all", label: "Tất cả trạng thái" },
  { value: "active", label: "Hoạt động" },
  { value: "inactive", label: "Đã khóa" },
]

const DepartmentList = () => {
  const [departments, setDepartments] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [showForm, setShowForm] = useState(false)
  const [editDepartment, setEditDepartment] = useState(null)
  const [search, setSearch] = useState("")
  const [searchInput, setSearchInput] = useState("")
  const [isSearching, setIsSearching] = useState(false)
  const [statusFilter, setStatusFilter] = useState("all")
  const [refresh, setRefresh] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const [menuOpen, setMenuOpen] = useState(null)
  const [openDropdown, setOpenDropdown] = useState(null)
  const [stats, setStats] = useState({
    total: 0,
    active: 0,
    inactive: 0,
    totalEmployees: 0,
  })

  const menuRef = useRef()
  const dropdownRef = useRef()
  const permissions = checkDepartmentPermissions()
  const navigate = useNavigate()

  // Lấy thông tin user hiện tại để kiểm tra quyền
  const getCurrentUser = () => {
    try {
      const userRaw = JSON.parse(localStorage.getItem('user') || '{}');
      return userRaw.user || userRaw;
    } catch {
      return {};
    }
  };

  const currentUser = getCurrentUser();
  const userRole = currentUser.role?.toLowerCase();

  // Kiểm tra quyền thêm phòng ban - admin, CEO và HR có quyền
  const canAddDepartment = ['admin', 'ceo', 'hr'].includes(userRole);

  // Debounce search input
  const debouncedSearch = (() => {
    let timeoutId;
    return (value) => {
      clearTimeout(timeoutId);
      setIsSearching(true);
      timeoutId = setTimeout(() => {
        setSearch(value);
        setIsSearching(false);
      }, 300);
    };
  })();

  // Handle search input change
  const handleSearchChange = (e) => {
    const value = e.target.value;
    setSearchInput(value);
    debouncedSearch(value);
  };

  // Clear search
  const handleClearSearch = () => {
    setSearchInput("");
    setSearch("");
  };

  useEffect(() => {
    fetchData()
  }, [refresh])



  useEffect(() => {
    const handleClickOutside = (event) => {
      if (menuRef.current && !menuRef.current.contains(event.target)) {
        setMenuOpen(null)
      }
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setOpenDropdown(null)
      }
    }
    document.addEventListener("mousedown", handleClickOutside)
    return () => document.removeEventListener("mousedown", handleClickOutside)
  }, [])

  const fetchData = async () => {
    setLoading(true)
    try {
      const deptRes = await getAllDepartments();
      const departmentsData = deptRes.data || [];
      setDepartments(departmentsData);
      setStats({
        total: departmentsData.length,
        active: departmentsData.filter((d) => d.isActive).length,
        inactive: departmentsData.filter((d) => !d.isActive).length,
        totalEmployees: departmentsData.reduce((sum, d) => sum + (d.memberCount || 0), 0),
      });
    } catch (err) {
      setError(err.message || 'Lỗi khi tải dữ liệu');
      showError('Lỗi khi tải dữ liệu');
    } finally {
      setLoading(false);
    }
  }

  // Lọc dữ liệu
  const filteredDepartments = departments.filter((dept) => {
    const matchSearch =
      dept.name.toLowerCase().includes(search.toLowerCase()) ||
      dept.code.toLowerCase().includes(search.toLowerCase()) ||
      (dept.head?.fullName?.toLowerCase().includes(search.toLowerCase()));
    const matchStatus =
      statusFilter === 'all' ||
      (statusFilter === 'active' && dept.isActive) ||
      (statusFilter === 'inactive' && !dept.isActive);
    return matchSearch && matchStatus;
  });

  // Phân trang
  const total = filteredDepartments.length
  const totalPages = Math.ceil(total / PAGE_SIZE)
  const pagedDepartments = filteredDepartments.slice((currentPage - 1) * PAGE_SIZE, currentPage * PAGE_SIZE)

  const handleAdd = () => {
    setEditDepartment(null)
    setShowForm(true)
  }

  const handleEdit = (dept) => {
    setEditDepartment(dept)
    setShowForm(true)
    setMenuOpen(null)
  }

  const handleDelete = async (dept) => {
    setMenuOpen(null)
    if (dept.memberCount > 0) {
      showError(`Không thể xóa phòng ban "${dept.name}" vì còn ${dept.memberCount} nhân viên`)
      return
    }

    if (!window.confirm(`Bạn chắc chắn muốn xóa phòng ban "${dept.name}"?`)) return

    try {
      await deleteDepartment(dept._id || dept.id)
      showSuccess("Xóa phòng ban thành công")
      setRefresh((r) => !r)
    } catch (err) {
      showError(err.message || "Lỗi khi xóa phòng ban")
    }
  }

  const handleFormSuccess = () => {
    setShowForm(false)
    setRefresh((r) => !r)
  }

  const handleDetail = (dept) => {
    navigate(`/departments/${dept._id || dept.id}`)
  }

  const handleViewEmployees = (dept) => {
    navigate(`/hr?department=${dept._id || dept.id}`)
    setMenuOpen(null)
  }

  const renderStatusBadge = (isActive) => (
    <span className={isActive ? 'hr-listuser-status-active' : 'hr-listuser-status-locked'}>
      {isActive ? "Hoạt động" : "Đã khóa"}
    </span>
  )

  const handleExportExcel = () => {
    try {
    // Tạo dữ liệu xuất Excel
      const exportData = filteredDepartments.map((dept, index) => ({
        "STT": index + 1,
      "Tên phòng ban": dept.name,
      "Mã phòng ban": dept.code,
      "Trưởng phòng": dept.head?.fullName || "Chưa có",
      "Email trưởng phòng": dept.head?.email || "",
        "Số nhân viên": dept.memberCount || 0,
      "Trạng thái": dept.isActive ? "Hoạt động" : "Đã khóa",
      "Ngày tạo": dept.createdAt ? new Date(dept.createdAt).toLocaleDateString("vi-VN") : "",
      }))

      // Tạo worksheet
      const ws = XLSX.utils.json_to_sheet(exportData)
      
      // Tạo workbook
      const wb = XLSX.utils.book_new()
      XLSX.utils.book_append_sheet(wb, ws, "Danh sách phòng ban")
      
      // Tạo tên file với timestamp
      const timestamp = new Date().toISOString().slice(0, 10).replace(/-/g, '')
      const fileName = `danh_sach_phong_ban_${timestamp}.xlsx`
      
      // Xuất file
      XLSX.writeFile(wb, fileName)
      
      showSuccess(`Đã xuất file Excel thành công: ${fileName}`)
    } catch (error) {
      console.error("Error exporting Excel:", error)
      showError("Có lỗi xảy ra khi xuất file Excel")
    }
  }

  const renderMenu = (dept) => (
    <div className="hr-listuser-action-menu" ref={menuRef}>
      <div className="hr-listuser-action-menu-title">Hành động</div>

      <div className="hr-listuser-action-menu-item" onClick={() => handleEdit(dept)}>
        Chỉnh sửa
        <span className="hr-listuser-action-menu-icon">
          <img src={editIcon} alt="edit" style={{ width: 18, height: 18 }} />
        </span>
      </div>

      <div className="hr-listuser-action-menu-item" onClick={() => handleViewEmployees(dept)}>
        Xem nhân viên ({dept.memberCount})
        <span className="hr-listuser-action-menu-icon">
          <img src={usersIcon} alt="users" style={{ width: 18, height: 18 }} />
        </span>
      </div>

      <div
        className="hr-listuser-action-menu-item"
        onClick={() => handleDelete(dept)}
        style={{
          color: dept.memberCount > 0 ? "#9ca3af" : "#dc2626",
          cursor: dept.memberCount > 0 ? "not-allowed" : "pointer",
          opacity: dept.memberCount > 0 ? 0.5 : 1,
        }}
      >
        Xóa phòng ban
        <span className="hr-listuser-action-menu-icon">
          <img src={trashIcon} alt="trash" style={{ width: 18, height: 18 }} />
        </span>
      </div>
    </div>
  )

  if (loading) {
    return (
      <div
        style={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          height: "400px",
          fontSize: 16,
          color: "#6b7280",
        }}
      >
        <div style={{ textAlign: "center" }}>
          <div
            style={{
              width: 40,
              height: 40,
              border: "4px solid #f3f4f6",
              borderTop: "4px solid #3b82f6",
              borderRadius: "50%",
              animation: "spin 1s linear infinite",
              margin: "0 auto 16px",
            }}
          ></div>
          Đang tải dữ liệu...
        </div>
        <style>{`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}</style>
      </div>
    )
  }

  return (
    <div>
      {/* Header layout giống HRLayout */}
      <div className="hr-layout-container">
        <div className="hr-layout-title-row">
          <img src={departmentIcon} alt="icon" style={{ width: 24, height: 24, color: '#2d5be3' }} />
              <div>
            <div className="hr-layout-title">Quản lý phòng ban</div>
            <div className="hr-layout-desc">Quản lý tổ chức và cơ cấu phòng ban</div>
              </div>
            </div>

        {/* Search box giống HRLayout */}
        <div className="hr-layout-search">
          <div className="hr-layout-search-input-wrap">
            <input
              type="text"
              placeholder="Tìm kiếm theo tên, mã phòng ban hoặc trưởng phòng..."
              className="hr-layout-search-input"
              value={searchInput}
              onChange={handleSearchChange}
            />
            <span className="hr-layout-search-icon">
              {isSearching ? (
                <div style={{
                  width: 16,
                  height: 16,
                  border: '2px solid #f3f3f3',
                  borderTop: '2px solid #2d5be3',
                  borderRadius: '50%',
                  animation: 'spin 1s linear infinite'
                }}></div>
              ) : (
                <img src={searchIcon} alt="search" style={{ width: 16, height: 16 }} />
              )}
            </span>
            {searchInput && (
              <button
                onClick={handleClearSearch}
                style={{
                  position: 'absolute',
                  right: '40px',
                  top: '50%',
                  transform: 'translateY(-50%)',
                  background: 'none',
                  border: 'none',
                  cursor: 'pointer',
                  fontSize: '16px',
                  color: '#999',
                  padding: '4px',
                  borderRadius: '50%',
                  width: '24px',
                  height: '24px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}
                title="Xóa tìm kiếm"
              >
                ×
              </button>
            )}
          </div>
        </div>

        {/* Toolbar giống HRLayout */}
        <div className="hr-toolbar-row">
          <div className="hr-toolbar-left" ref={dropdownRef}>
            <div className="hr-dropdown">
              <button className="hr-dropdown-btn" onClick={() => setOpenDropdown(openDropdown === 'status' ? null : 'status')}>
                <span>{statusOptions.find(opt => opt.value === statusFilter)?.label || 'Tất cả trạng thái'}</span>
                <img src={dropdownIcon} alt="dropdown" className="dropdown-icon" />
                </button>
              {openDropdown === 'status' && (
                <div className="hr-dropdown-menu" style={{ display: 'block' }}>
                  {statusOptions.map((opt) => (
                    <div key={opt.value} className="hr-dropdown-item" onClick={() => { setStatusFilter(opt.value); setCurrentPage(1); setOpenDropdown(null); }}>
                      {opt.label}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          <div className="hr-toolbar-actions">
            <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
              <span className="hr-toolbar-count">
                Hiển thị {pagedDepartments.length}/{total} phòng ban
              </span>
            </div>
            <button className="hr-toolbar-btn" onClick={handleExportExcel}>
              <img src={downloadIcon} alt="excel" style={{ width: 18, height: 18 }} /> Xuất Excel
            </button>
            {canAddDepartment && (
              <button className="hr-toolbar-btn hr-toolbar-btn-primary" onClick={handleAdd}>
                <img src={addIcon} alt="add" style={{ width: 18, height: 18 }} /> 
                Thêm phòng ban
              </button>
            )}
          </div>
          </div>
        </div>

        {/* Bảng dữ liệu */}
      <div className="hr-listuser-container">
        <table className="hr-listuser-table">
            <thead>
            <tr>
              <th>Phòng ban</th>
              <th>Mã</th>
              <th>Trưởng phòng</th>
              <th>Nhân viên</th>
              <th>Trạng thái</th>
              <th>Hành động</th>
              </tr>
            </thead>
            <tbody>
            {loading ? (
              Array.from({ length: 8 }).map((_, idx) => (
                <tr key={idx} style={{ opacity: 0.7 }}>
                  <td className="hr-listuser-td">
                    <div style={{ width: 120, height: 16, background: '#f0f0f0', borderRadius: 4, animation: 'pulse 1.5s ease-in-out infinite' }}></div>
                  </td>
                  <td className="hr-listuser-td"><div style={{ width: 80, height: 14, background: '#f0f0f0', borderRadius: 4, animation: 'pulse 1.5s ease-in-out infinite' }}></div></td>
                  <td className="hr-listuser-td"><div style={{ width: 100, height: 14, background: '#f0f0f0', borderRadius: 4, animation: 'pulse 1.5s ease-in-out infinite' }}></div></td>
                  <td className="hr-listuser-td"><div style={{ width: 60, height: 14, background: '#f0f0f0', borderRadius: 4, animation: 'pulse 1.5s ease-in-out infinite' }}></div></td>
                  <td className="hr-listuser-td"><div style={{ width: 80, height: 14, background: '#f0f0f0', borderRadius: 4, animation: 'pulse 1.5s ease-in-out infinite' }}></div></td>
                  <td className="hr-listuser-td"><div style={{ width: 40, height: 14, background: '#f0f0f0', borderRadius: 4, animation: 'pulse 1.5s ease-in-out infinite' }}></div></td>
                </tr>
              ))
            ) : pagedDepartments.length === 0 ? (
              <tr>
                <td colSpan={6} style={{ textAlign: 'center', padding: '60px 20px', color: '#666', fontSize: '16px' }}>
                  <div style={{ fontSize: '48px', marginBottom: '16px', opacity: 0.3 }}>🔍</div>
                  <div style={{ marginBottom: '8px', fontWeight: '500' }}>Không tìm thấy phòng ban</div>
                  <div style={{ fontSize: '14px' }}>Thử điều chỉnh bộ lọc hoặc từ khóa tìm kiếm</div>
                </td>
              </tr>
            ) : (
              pagedDepartments.map((dept) => (
                <tr key={dept._id || dept.id}>
                  <td className="hr-listuser-td">
                    <div onClick={() => handleDetail(dept)} style={{ cursor: "pointer" }}>
                      <div style={{ fontWeight: 500, color: "#2d5be3", marginBottom: 2 }}>
                        {dept.name}
                      </div>
                      {dept.description && (
                        <div style={{ color: "#888", fontSize: 13 }}>
                          {dept.description.length > 50 ? dept.description.substring(0, 50) + "..." : dept.description}
                        </div>
                      )}
                    </div>
                  </td>

                  <td className="hr-listuser-td">
                    <span className="hr-listuser-department">
                      {dept.code}
                    </span>
                  </td>

                  <td className="hr-listuser-td">
                    {dept.head ? (
                      <div style={{ display: "flex", alignItems: "center", gap: 8, justifyContent: 'center' }}>
                        {dept.head.avatar && (
                          <img
                            src={dept.head.avatar}
                            alt={dept.head.fullName}
                            className="hr-listuser-avatar"
                            style={{ width: 32, height: 32 }}
                          />
                        )}
                        <div style={{ textAlign: 'left' }}>
                          <div style={{ fontWeight: 500, fontSize: 14 }}>
                            {dept.head.fullName}
                          </div>
                          <div style={{ color: "#888", fontSize: 12 }}>
                            {dept.head.email}
                          </div>
                        </div>
                      </div>
                    ) : (
                      <span style={{ color: "#bbb", fontStyle: "italic" }}>Chưa có</span>
                    )}
                  </td>

                  <td className="hr-listuser-td">
                    <span
                      style={{
                        background: dept.memberCount > 0 ? "#A3BFFA" : "#f3f4f6",
                        color: dept.memberCount > 0 ? "#000" : "#666",
                        padding: "3px 12px",
                        borderRadius: 12,
                        fontSize: 12,
                        fontWeight: 400,
                      }}
                    >
                      {dept.memberCount}
                    </span>
                  </td>

                  <td className="hr-listuser-td">{renderStatusBadge(dept.isActive)}</td>

                  <td className="hr-listuser-td" style={{ position: "relative" }}>
                    <span
                      className="hr-listuser-action"
                      onClick={() => setMenuOpen(menuOpen === (dept._id || dept.id) ? null : dept._id || dept.id)}
                      style={{ cursor: 'pointer' }}
                    >
                      •••
                    </span>
                    {menuOpen === (dept._id || dept.id) && renderMenu(dept)}
                  </td>
                </tr>
              ))
              )}
            </tbody>
          </table>

        {/* Phân trang */}
        {totalPages > 1 && (
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              gap: 8,
              marginTop: 24,
            }}
          >
            <button
              disabled={currentPage === 1}
              onClick={() => setCurrentPage((p) => p - 1)}
              style={{
                padding: "8px 16px",
                border: "1px solid #ddd",
                background: "#fff",
                color: currentPage === 1 ? "#999" : "#333",
                borderRadius: 6,
                cursor: currentPage === 1 ? "not-allowed" : "pointer",
              }}
            >
              ← Trước
            </button>

            {[...Array(totalPages)].map((_, i) => (
              <button
                key={i}
                onClick={() => setCurrentPage(i + 1)}
                style={{
                  padding: "8px 12px",
                  border: "1px solid",
                  borderColor: currentPage === i + 1 ? "#2d5be3" : "#ddd",
                  background: currentPage === i + 1 ? "#2d5be3" : "#fff",
                  color: currentPage === i + 1 ? "#fff" : "#333",
                  borderRadius: 6,
                  fontWeight: 500,
                  cursor: "pointer",
                  minWidth: 40,
                }}
              >
                {i + 1}
              </button>
            ))}

            <button
              disabled={currentPage === totalPages}
              onClick={() => setCurrentPage((p) => p + 1)}
              style={{
                padding: "8px 16px",
                border: "1px solid #ddd",
                background: "#fff",
                color: currentPage === totalPages ? "#999" : "#333",
                borderRadius: 6,
                cursor: currentPage === totalPages ? "not-allowed" : "pointer",
              }}
            >
              Sau →
            </button>
          </div>
        )}
      </div>

      {/* Form modal */}
      {showForm && (
        <DepartmentForm department={editDepartment} onSuccess={handleFormSuccess} onCancel={() => setShowForm(false)} />
      )}
      
      <style>{`
        @keyframes pulse {
          0% { opacity: 1; }
          50% { opacity: 0.5; }
          100% { opacity: 1; }
        }
        .hr-dropdown-menu {
          display: none;
        }
        .hr-dropdown-menu[style*="display: block"] {
          display: block !important;
        }
      `}</style>
    </div>
  )
}

export default DepartmentList

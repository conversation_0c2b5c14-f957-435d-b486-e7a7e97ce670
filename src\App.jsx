import { BrowserRouter, useLocation } from 'react-router-dom';
import AppRoutes from './routes/AppRoutes.jsx';
import ScrollToTop from './components/ScrollToTop.jsx';
import MaintenancePage from './components/MaintenancePage.jsx';
import LogoutConfirmation from './components/LogoutConfirmation.jsx';
import { useEffect, useState } from 'react';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { getProfile } from './api/profile';
import { AUTH_ENDPOINTS } from './api/endpoints';
import { showWarning } from './utils/toastUtils';

function PollingLogout() {
  const location = useLocation();
  const [showLogoutConfirm, setShowLogoutConfirm] = useState(false);
  const [logoutMessage, setLogoutMessage] = useState('');

  useEffect(() => {
    // Các path không cần kiểm tra
    const excludePaths = ['/login', '/forgotpassword', '/resetpassword'];
    // Kiểm tra cả path reset-password/:token
    const isExcluded = excludePaths.some(p => location.pathname.startsWith(p)) || location.pathname.startsWith('/reset-password');
    if (isExcluded) return;

    // Kiểm tra xem user có phải admin không
    const checkUserRole = () => {
      try {
        const currentUser = JSON.parse(localStorage.getItem('user') || '{}');
        const user = currentUser.user || currentUser;
        return user.role === 'admin';
      } catch {
        return false;
      }
    };

    // Lấy token để gọi API
    const getAuthToken = () => {
      try {
        const tokenFromStorage = localStorage.getItem('token');
        const authToken = localStorage.getItem('authToken');
        const user = JSON.parse(localStorage.getItem('user') || '{}');
        const userToken = user.token;
        return tokenFromStorage || authToken || userToken;
      } catch {
        return null;
      }
    };

    const checkSession = async () => {
      const token = getAuthToken();
      if (!token) {
        window.location.href = '/login';
        return;
      }

      try {
        // Gọi session monitor endpoint thay vì profile
        const response = await fetch(AUTH_ENDPOINTS.SESSION_MONITOR, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        const result = await response.json();

        if (!response.ok) {
          // Nếu session không hợp lệ
          if (result.data && result.data.shouldRedirectToLogin) {
            if (checkUserRole() && result.data.reason && result.data.reason.includes('thiết bị khác')) {
              // Admin bị force logout - hiển thị thông báo
              setLogoutMessage(result.data.reason);
              setShowLogoutConfirm(true);
              showWarning('Có người khác đã đăng nhập vào tài khoản admin của bạn!');
            } else {
              // Các trường hợp khác - logout ngay
              localStorage.clear();
              window.location.href = '/login';
            }
          }
        } else {
          // Session hợp lệ - kiểm tra profile để check block/lock status
          const profileRes = await getProfile();
          const user = profileRes.data || profileRes;
          if (user.status === 'locked' || user.isBlocked === true) {
            localStorage.clear();
            window.location.href = '/login';
          }
        }
      } catch (error) {
        console.error('Session check error:', error);
          // Nếu là admin và lỗi 503 (maintenance mode), không logout
          if (checkUserRole() && error.message && error.message.includes('503')) {
            return;
          }
          
          // Các lỗi khác hoặc non-admin user
          localStorage.clear();
          window.location.href = '/login';
      }
    };

    const interval = setInterval(checkSession, 10000);
    return () => clearInterval(interval);
  }, [location.pathname]);

  const handleLogoutConfirm = () => {
    setShowLogoutConfirm(false);
    // Cleanup sẽ được xử lý bởi LogoutConfirmation component
  };

  return (
    <LogoutConfirmation
      show={showLogoutConfirm}
      message={logoutMessage}
      onConfirm={handleLogoutConfirm}
    />
  );
}

function MaintenanceChecker() {
  const location = useLocation();
  const [maintenanceData, setMaintenanceData] = useState(null);
  const [isMaintenanceMode, setIsMaintenanceMode] = useState(false);
  const [loading, setLoading] = useState(true);

  const checkMaintenanceStatus = async () => {
    // Bỏ qua kiểm tra nếu đang ở trang login, forgotpassword, resetpassword
    const excludePaths = ['/login', '/forgotpassword', '/resetpassword'];
    const isExcluded = excludePaths.some(p => location.pathname.startsWith(p)) || location.pathname.startsWith('/reset-password');
    if (isExcluded) {
      setIsMaintenanceMode(false);
      setLoading(false);
      return;
    }
    try {
      // Lấy thông tin user để kiểm tra role
      const currentUser = JSON.parse(localStorage.getItem('user') || '{}');
      const user = currentUser.user || currentUser;
      const token = localStorage.getItem('token') || localStorage.getItem('authToken') || user.token;
      // Nếu không có token, skip maintenance check để cho login bình thường
      if (!token) {
        setIsMaintenanceMode(false);
        setLoading(false);
        return;
      }
      // Nếu là admin và có token thì không cần kiểm tra maintenance
      if (user.role === 'admin' && token) {
        setIsMaintenanceMode(false);
        setLoading(false);
        return;
      }
      // Gọi API kiểm tra trạng thái maintenance (public endpoint)
      const response = await fetch('/api/maintenance/status');
      const result = await response.json();
      // Admin không bao giờ bị ảnh hưởng bởi maintenance mode
      if (user.role === 'admin') {
        setIsMaintenanceMode(false);
      } else if (result.success && result.data.maintenance.enabled) {
        setIsMaintenanceMode(true);
        setMaintenanceData(result.data.maintenance);
      } else {
        setIsMaintenanceMode(false);
      }
    } catch (error) {
      // Nếu lỗi kết nối, coi như không maintenance
      console.error('Error checking maintenance status:', error);
      setIsMaintenanceMode(false);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    checkMaintenanceStatus();
    // Chỉ set interval cho non-admin users và không ở trang login/forgot/reset
    const excludePaths = ['/login', '/forgotpassword', '/resetpassword'];
    const isExcluded = excludePaths.some(p => location.pathname.startsWith(p)) || location.pathname.startsWith('/reset-password');
    if (isExcluded) return;
    const currentUser = JSON.parse(localStorage.getItem('user') || '{}');
    const user = currentUser.user || currentUser;
    if (user.role !== 'admin') {
      // Kiểm tra lại mỗi 30 giây chỉ cho non-admin
      const interval = setInterval(checkMaintenanceStatus, 30000);
      return () => clearInterval(interval);
    }
    // Admin không cần interval check
  }, [location.pathname]);

  if (loading) {
    return (
      <div style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '100vh',
        background: '#f5f5f5'
      }}>
        <div style={{
          textAlign: 'center',
          color: '#666'
        }}>
          <div style={{
            width: '40px',
            height: '40px',
            border: '4px solid #e0e0e0',
            borderTop: '4px solid #2196f3',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite',
            margin: '0 auto 16px'
          }}></div>
          Đang kiểm tra trạng thái hệ thống...
        </div>
      </div>
    );
  }

  if (isMaintenanceMode) {
    return <MaintenancePage maintenanceData={maintenanceData} />;
  }

  return null;
}

function App() {
  useEffect(() => {
    if ('scrollRestoration' in window.history) {
      window.history.scrollRestoration = 'manual';
    }
    window.scrollTo(0, 0);
  }, []);

  return (
    <BrowserRouter basename="/">
      <MaintenanceChecker />
      <PollingLogout />
      <ScrollToTop />
      <AppRoutes/>
      <ToastContainer
        position="top-right"
        autoClose={3000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="light"
      />
    </BrowserRouter>
  );
}
export default App

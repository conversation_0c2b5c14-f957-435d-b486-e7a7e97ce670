@import url('../index.css');
@import url('https://fonts.googleapis.com/css2?family=Be+Vietnam+Pro:wght@400;500;600;700&display=swap');

/* Notification Popup Styles */
.notification-popup {
  width: 420px;
  height: 600px;
  background: white;
  border: 1px solid #E5E7EB;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  font-family: 'Be Vietnam Pro', sans-serif;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.notification-popup-header {
  background: white;
  padding: 14px 14px 0 14px;
  position: relative;
  z-index: 1000;
}

.notification-popup-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  position: relative;
}

.notification-popup-title h3 {
  font-size: 20px;
  font-weight: 700;
  color: #5B5B5B;
  margin: 0;
  flex-shrink: 0;
}

.notification-popup-title .filter-dropdown-wrapper {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1002;
  display: inline-block;
  width: 95px;
}

.notification-popup-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.filter-dropdown-wrapper {
  position: relative;
  display: inline-block;
}

.filter-btn-popup {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px 12px 8px 8px;
  background: white;
  border: 1px solid #D1D5DB;
  border-radius: 12px;
  color: #6B7280;
  font-size: 14px;
  font-weight: 400;
  cursor: pointer;
  transition: all 0.2s ease;
  width: 100px;
  position: relative;
}

.filter-btn-popup svg {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
}

.filter-btn-popup span {
  margin-right: auto;
  text-align: left;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
}

.filter-btn-popup:hover {
  background: #F9FAFB;
  border-color: #9CA3AF;
}

.filter-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  width: 100px;
  background-color: white;
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  box-shadow: 0 4px 24px rgba(0,0,0,0.12);
  padding: 8px 4px;
  z-index: 10;
  margin-top: 5px;
  animation: fadeIn 0.15s;
}

.filter-dropdown.show {
  opacity: 1;
  pointer-events: auto;
}

/* .filter-dropdown-centered {
  width: 100px;
} */

.filter-option {
  display: block;
  width: 100%;
  padding: 6px;
  background: transparent;
  border: 1px solid #fff;
  margin-bottom: 5px;
  border-radius: 8px;
  text-align: center;
  color: #6B7280;
  font-size: 14px;
  font-weight: 400;
  cursor: pointer;
  transition: all 0.2s ease;
}

.filter-option.active {
  background: #3B82F6;
  color: white;
  font-weight: 500;
}

.filter-option.active:hover {
  background: #2563EB;
}

.filter-option:hover {
  background: #F9FAFB;
}

.more-actions-dropdown-wrapper {
  position: relative;
}

.more-actions-popup {
  padding: 6px;
  background: transparent;
  border: none;
  border-radius: 4px;
  color: #6B7280;
  cursor: pointer;
  transition: all 0.2s ease;
}

.more-actions-popup:hover {
  background: #F3F4F6;
  color: #374151;
}

.more-actions-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  transform: translateY(-10px);
  z-index: 1001;
  margin-top: 4px;
  background: white;
  border: 1px solid #D1D5DB;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  min-width: 220px;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease, transform 0.3s ease;
  overflow: hidden;
  padding: 8px 0;
}

.more-actions-dropdown.show {
  opacity: 1;
  transform: translateY(0);
  pointer-events: auto;
}

.more-actions-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 8px 8px;
  background: transparent;
  border: none;
  text-align: left;
  color: #1F2937;
  font-size: 14px;
  font-weight: 400;
  cursor: pointer;
  transition: all 0.2s ease;
}

.more-actions-option:hover {
  background: #F9FAFB;
}

.more-actions-option-text {
  flex: 1;
}

.more-actions-option-checkbox {
  width: 14px;
  height: 14px;
  border: 2px solid #3B82F6;
  background: transparent;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.more-actions-option-checkbox.checked {
  background: #3B82F6;
  border-color: #3B82F6;
}

.more-actions-option-checkbox svg {
  color: white;
}

.notification-popup-tabs {
  display: flex;
  gap: 4px;
  padding-bottom: 6px;
  border-bottom: 1px solid #E5E7EB;
  margin: 0;
}

.tab-button-popup {
  padding: 6px 12px;
  background: transparent;
  border-radius: 6px;
  border: 1px solid #fff;
  color: #5B5B5B;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.tab-button-popup:hover {
  color: #374151;
  background: #F9FAFB;
}

.tab-button-popup.active {
  color: #007BFF;
  background: #F6F6F6;
}

.notification-popup-content {
  flex: 1;
  overflow-y: auto;
  padding: 14px;
  min-height: 0;
}

/* Override styles for popup content */
.notification-popup .notification-filters {
  padding: 12px 20px;
  margin: 0;
  border-bottom: 1px solid #F3F4F6;
}

.notification-popup .notification-filters button {
  padding: 4px 8px;
  font-size: 11px;
}

.notification-popup .notifications-list {
  padding: 0;
  gap: 0;
}

.notification-popup .notification-item {
  padding: 16px 20px;
  border: none;
  border-bottom: 1px solid #F3F4F6;
  border-radius: 0;
  margin: 0;
}

.notification-popup .notification-item:hover {
  background: #F9FAFB;
}

.notification-popup .notification-item:last-child {
  border-bottom: none;
}

.notification-popup .notification-header h4 {
  font-size: 14px;
}

.notification-popup .notification-message {
  font-size: 13px;
  margin: 4px 0 8px 0;
}

.notification-popup .notification-time {
  font-size: 11px;
}

.notification-popup .priority-badge,
.notification-popup .category-badge {
  font-size: 10px;
  padding: 1px 6px;
}

.notification-popup .no-notifications {
  padding: 40px 20px;
  text-align: center;
}

.notification-popup .no-notifications p {
  font-size: 14px;
  color: #6B7280;
  margin: 0;
}

/* Simple notification item styles */
.notification-item-simple {
  display: flex;
  align-items: flex-start;
  padding: 12px 20px;
  margin-bottom: 10px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  background: white;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.notification-item-simple:hover {
  background: #F9FAFB;
}

.notification-item-simple:last-child {
  border-bottom: none;
}

.notification-item-simple.unread {
  background: linear-gradient(to right, #FFF5F7, #FEF2F4)
}

.notification-item-simple.read {
  background: white;
}

.notification-left-border {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: #3B82F6;
  border-radius: 8px 0 0 8px;
}

.notification-item-simple.read .notification-left-border {
  background: #EAECF0;
}

.notification-simple-content {
  flex: 1;
  min-width: 0;
}

.notification-sender {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 4px;
  line-height: 1.3;
}

.notification-text {
  font-size: 12px;
  color: #6B7280;
  line-height: 1.4;
}

.notification-simple-time {
  color: #9CA3AF;
  font-weight: 400;
}

/* Responsive for popup */
@media (max-width: 480px) {
  .notification-popup {
    width: 350px;
    height: 500px;
  }

  .notification-popup-content {
    flex: 1;
    min-height: 0;
  }
}

/* Animation for dropdown */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Notification dropdown wrapper */
.notification-dropdown-wrapper {
  position: absolute;
  top: 100%;
  right: 0;
  z-index: 1000;
  margin-top: 8px;
  animation: fadeIn 0.3s ease;
} 
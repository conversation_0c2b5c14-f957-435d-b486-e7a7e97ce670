/* Timeline Component Styles */
.timeline-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: #fff;
  position: relative;
}

.timeline-header {
  padding: 9px;
  border-bottom: 1px solid #eaeaea;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  height: 60px;
}

.month-selector {
  display: flex;
  align-items: center;
  position: absolute;
  left: 16px;
  top: 16px;
  z-index: 3;
}

.month-button {
  background-color: #ffffff;
  border: 1px solid #e0e0e0;
  border-radius: 80px;
  padding: 10px 16px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.2s;
  outline: none;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  color: #5D5D5D;
}

.month-button:hover {
  background-color: #f5f5f5;
}

.month-button .icon {
  margin-right: 8px;
  display: flex;
  align-items: center;
}

.timeline-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.timeline-sidebar {
  width: 240px;
  border-right: 1px solid #eaeaea;
  background-color: #fff;
}

.sidebar-header {
  padding: 10px 16px;
  font-weight: 600;
  border-bottom: 1px solid #eaeaea;
  background-color: #f9f9f9;
  font-size: 14px;
  color: #4b5563;
  display: flex;
  align-items: center;
}

.member-count {
  background-color: #f0f0f0;
  border-radius: 4px;
  padding: 2px 6px;
  font-size: 12px;
  margin-left: 8px;
  color: #666;
}

.team-members-list {
  padding: 0;
}

.team-member {
  border-bottom: 1px solid #f0f0f0;
}

.member-checkbox-label {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  width: 100%;
  cursor: pointer;
}

.member-checkbox {
  margin-right: 12px;
  cursor: pointer;
}

.team-header {
  background-color: #f9f9f9;
}

.team-header .team-name {
  color: #68966a;
  font-weight: 600;
  font-size: 14px;
}

.team-header:nth-of-type(8) .team-name {
  color: #6aadcf;
}

.member-info-container {
  display: flex;
  align-items: center;
  flex: 1;
}

.member-avatar {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: #ffa500;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  overflow: hidden;
}

.member-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.member-info {
  display: flex;
  flex-direction: column;
}

.member-name {
  font-size: 14px;
  font-weight: 500;
}

.member-role {
  font-size: 12px;
  color: #666;
}

/* Custom checkbox styling */
.member-checkbox {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  width: 16px;
  height: 16px;
  border: 1px solid #ccc;
  border-radius: 3px;
  outline: none;
  transition: all 0.2s;
  position: relative;
  background-color: white;
}

.member-checkbox:checked {
  background-color: #4299e1;
  border-color: #4299e1;
}

.member-checkbox:checked::after {
  content: '';
  position: absolute;
  width: 5px;
  height: 10px;
  border: solid white;
  border-width: 0 2px 2px 0;
  top: 1px;
  left: 5px;
  transform: rotate(45deg);
}

.member-checkbox:hover {
  border-color: #4299e1;
}

/* Timeline Grid Styling */
.timeline-grid-container {
  flex: 1;
  overflow-x: auto;
  background-color: #fff;
  position: relative;
}

.timeline-days-header {
  display: flex;
  position: sticky;
  top: 0;
  background-color: #fff;
  z-index: 2;
  border-bottom: 1px solid #eaeaea;
}

.day-column {
  flex: 0 0 60px;
  min-width: 60px;
  text-align: center;
  padding: 8px 4px;
  border-right: none;
  border-bottom: none;
}

/* Nhóm các ngày thứ 2 đến thứ 7 */
.day-column:not(.sunday) {
  background-color: #ffffff;
}

.day-number {
  font-size: 14px;
  font-weight: 600;
}

.weekday {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
}

/* Sunday column styling */
.day-column.sunday {
  background-color: #f0f0f0;
  border-left: 1px solid #ddd;
  border-right: 1px solid #ddd;
}

/* Today styling */
.day-column.today {
  background-color: #1a73e8;
  color: white;
  position: relative;
  z-index: 1;
}

.day-column.today .weekday {
  color: rgba(255, 255, 255, 0.8);
}

.current-day-indicator {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background-color: #ff5252;
}

.timeline-grid {
  position: relative;
}

.timeline-row {
  height: 50px;
  border-bottom: none; /* Ẩn đường kẻ ngang */
  position: relative;
}

/* Thêm đường kẻ ngang mờ cho mỗi hàng team */
.team-header + .team-member {
  border-top: 1px solid #f0f0f0;
}

.timeline-task {
  position: absolute;
  height: 36px;
  top: 7px;
  border-radius: 4px;
  padding: 0 8px;
  display: flex;
  align-items: center;
  font-size: 13px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  z-index: 2; /* Đảm bảo task hiển thị trên cùng */
}

.task-pink {
  background-color: #ffcdd2;
  color: #c62828;
}

.task-yellow {
  background-color: #fff9c4;
  color: #f57f17;
}

.task-blue {
  background-color: #bbdefb;
  color: #1565c0;
}

/* Custom scrollbar styles */
.timeline-sidebar::-webkit-scrollbar,
.timeline-grid-container::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.timeline-sidebar::-webkit-scrollbar-track,
.timeline-grid-container::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.timeline-sidebar::-webkit-scrollbar-thumb,
.timeline-grid-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.timeline-sidebar::-webkit-scrollbar-thumb:hover,
.timeline-grid-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* For team members with a checkbox (from the reference image) */
.team-member input[type="checkbox"] {
  margin-right: 8px;
}

/* Date Picker Styles */
.date-picker-container {
  position: absolute;
  top: 100%;
  left: 0;
  margin-top: 8px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  width: 280px;
  z-index: 100;
}

.date-picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border-bottom: 1px solid #eaeaea;
}

.date-picker-current-month {
  font-weight: 600;
  font-size: 14px;
  color: #333;
}

.month-nav-btn {
  background: none;
  border: none;
  cursor: pointer;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  font-size: 16px;
  color: #666;
}

.month-nav-btn:hover {
  background-color: #f0f0f0;
}

.date-picker-weekdays {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  padding: 8px 12px;
  border-bottom: 1px solid #eaeaea;
}

.date-picker-weekday {
  text-align: center;
  font-size: 12px;
  font-weight: 600;
  color: #666;
  padding: 4px 0;
}

.date-picker-days {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  padding: 8px 12px;
}

.date-picker-day {
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 50%;
  font-size: 13px;
  margin: 2px;
  color: #333;
}

.date-picker-day:hover:not(.other-month) {
  background-color: #f0f0f0;
}

.date-picker-day.other-month {
  color: #ccc;
  cursor: default;
}

.date-picker-day.today {
  background-color: #e6f7ff;
  font-weight: 600;
}

.date-picker-day.selected {
  background-color: #1a73e8;
  color: white;
  font-weight: 600;
}

.date-picker-actions {
  display: flex;
  justify-content: flex-end;
  padding: 12px;
  border-top: 1px solid #eaeaea;
}

.today-btn {
  background-color: #f0f0f0;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 13px;
  cursor: pointer;
  color: #333;
}

.today-btn:hover {
  background-color: #e0e0e0;
}

.date-picker-selectors {
  display: flex;
  position: relative;
}

.month-selector-btn,
.year-selector-btn {
  background: none;
  border: none;
  font-size: 14px;
  font-weight: 500;
  color: #333;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  display: flex;
  align-items: center;
}

.month-selector-btn:hover,
.year-selector-btn:hover {
  background-color: #f0f0f0;
}

.month-selector-btn {
  margin-right: 4px;
}

.dropdown-arrow {
  font-size: 10px;
  margin-left: 4px;
  color: #666;
}

.year-select-dropdown,
.month-select-dropdown {
  position: absolute;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 5;
  width: 120px;
  max-height: 200px;
  overflow-y: auto;
}

.year-select-dropdown {
  right: 0;
  top: 100%;
  margin-top: 4px;
}

.month-select-dropdown {
  left: 0;
  top: 100%;
  margin-top: 4px;
}

.year-select-list,
.month-select-list {
  padding: 4px 0;
}

.year-option,
.month-option {
  padding: 8px 12px;
  cursor: pointer;
  font-size: 14px;
}

.year-option:hover,
.month-option:hover {
  background-color: #f0f0f0;
}

.year-option.selected,
.month-option.selected {
  background-color: #e6f7ff;
  font-weight: 600;
}

/* Scrollbar styles for dropdowns */
.year-select-dropdown::-webkit-scrollbar,
.month-select-dropdown::-webkit-scrollbar {
  width: 6px;
}

.year-select-dropdown::-webkit-scrollbar-track,
.month-select-dropdown::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.year-select-dropdown::-webkit-scrollbar-thumb,
.month-select-dropdown::-webkit-scrollbar-thumb {
  background: #ddd;
  border-radius: 3px;
}

.year-select-dropdown::-webkit-scrollbar-thumb:hover,
.month-select-dropdown::-webkit-scrollbar-thumb:hover {
  background: #ccc;
} 
// Add cache and preload logic at the top
let statsCache = null;
let statsCacheTimestamp = 0;
const STATS_CACHE_DURATION = 10000; // 10 seconds
let statsPreloadPromise = null;

import React, { useEffect, useState } from "react";
import fileText from "../../../assets/file-text.svg";
import complete from "../../../assets/square-check-big.svg";
import inprogress from "../../../assets/eye.svg";
import overdue from "../../../assets/Notification.svg";
import { STATISTICS_ENDPOINTS } from "../../../api/endpoints";
import { getAllProjects } from "../../../api/projectManagement";
import {
  getMyProjectTasks,
  getProjectTasks,
} from "../../../api/taskManagement";
import { getAllUsers } from "../../../api/userManagement";
import { getProfile } from "../../../api/profile";

const preloadStats = async () => {
  if (statsPreloadPromise) return statsPreloadPromise;
  statsPreloadPromise = (async () => {
    try {
      const userRaw = JSON.parse(localStorage.getItem("user") || "{}");
      const userId =
        userRaw._id ||
        userRaw.id ||
        (userRaw.user && (userRaw.user._id || userRaw.user.id));
      const role = (userRaw.role || userRaw.user?.role || "").toLowerCase();
      let totalProjects = 0,
        completedTasks = 0,
        inProgressTasks = 0,
        overdueTasks = 0;
      if (role === "admin" || role === "ceo") {
        const projectRes = await getAllProjects();
        const projects = projectRes.data || projectRes;
        totalProjects = projects.length;
        // Tối ưu: lấy tasks song song
        const allTasksArr = await Promise.all(
          projects.map((project) =>
            getProjectTasks(project._id || project.id)
              .then((taskRes) => taskRes.data || taskRes)
              .catch(() => [])
          )
        );
        const allTasks = allTasksArr.flat();
        completedTasks = allTasks.filter(
          (t) => t.status === "completed" || t.status === "done"
        ).length;
        inProgressTasks = allTasks.filter(
          (t) => t.status === "in_progress" || t.status === "in-progress"
        ).length;
        overdueTasks = allTasks.filter((t) => t.status === "overdue").length;
      } else if (role === "departmenthead") {
        const profile = await getProfile();
        const myDeptId =
          profile.data?.departmentId?._id ||
          profile.data?.departmentId ||
          profile.data?.department?._id;
        const usersRes = await getAllUsers();
        const users = usersRes.data || usersRes;
        const deptUserIds = users
          .filter(
            (u) =>
              u.departmentId?._id === myDeptId || u.departmentId === myDeptId
          )
          .map((u) => u._id || u.id);
        const projectRes = await getAllProjects();
        const projects = projectRes.data || projectRes;
        totalProjects = projects.filter((p) =>
          deptUserIds.includes(p.leaderId?._id || p.leaderId?.id)
        ).length;
        // Tối ưu: lấy tasks song song
        const allTasksArr = await Promise.all(
          projects.map((project) =>
            getProjectTasks(project._id || project.id)
              .then((taskRes) => taskRes.data || taskRes)
              .catch(() => [])
          )
        );
        const allTasks = allTasksArr.flat();
        const deptTasks = allTasks.filter(
          (t) =>
            deptUserIds.includes(
              t.assignedToId?._id ||
                t.assignedToId?.id ||
                t.assignedTo?._id ||
                t.assignedTo?.id
            ) || deptUserIds.includes(t.createdBy?._id || t.createdBy?.id)
        );
        completedTasks = deptTasks.filter(
          (t) => t.status === "completed" || t.status === "done"
        ).length;
        inProgressTasks = deptTasks.filter(
          (t) => t.status === "in_progress" || t.status === "in-progress"
        ).length;
        overdueTasks = deptTasks.filter((t) => t.status === "overdue").length;
      } else if (role === "leader") {
        const projectRes = await getAllProjects();
        const projects = projectRes.data || projectRes;
        const myProjects = projects.filter(
          (p) => p.leaderId?._id === userId || p.leaderId?.id === userId
        );
        totalProjects = myProjects.length;
        // Tối ưu: lấy tasks song song
        const allTasksArr = await Promise.all(
          myProjects.map((project) =>
            getProjectTasks(project._id || project.id)
              .then((taskRes) => taskRes.data || taskRes)
              .catch(() => [])
          )
        );
        const allTasks = allTasksArr.flat();
        completedTasks = allTasks.filter(
          (t) => t.status === "completed" || t.status === "done"
        ).length;
        inProgressTasks = allTasks.filter(
          (t) => t.status === "in_progress" || t.status === "in-progress"
        ).length;
        overdueTasks = allTasks.filter((t) => t.status === "overdue").length;
      } else {
        const projectRes = await getAllProjects();
        const projects = projectRes.data || projectRes;
        const myProjects = projects.filter(
          (p) =>
            Array.isArray(p.members) &&
            p.members.some((m) => m.userId === userId || m._id === userId)
        );
        totalProjects = myProjects.length;
        // Tối ưu: lấy tasks song song
        const allTasksArr = await Promise.all(
          myProjects.map((project) =>
            getProjectTasks(project._id || project.id)
              .then((taskRes) => taskRes.data || taskRes)
              .catch(() => [])
          )
        );
        const allTasks = allTasksArr.flat();
        completedTasks = allTasks.filter(
          (t) => t.status === "completed" || t.status === "done"
        ).length;
        inProgressTasks = allTasks.filter(
          (t) => t.status === "in_progress" || t.status === "in-progress"
        ).length;
        overdueTasks = allTasks.filter((t) => t.status === "overdue").length;
      }
      const stats = {
        totalProjects,
        completedTasks,
        inProgressTasks,
        overdueTasks,
      };
      statsCache = stats;
      statsCacheTimestamp = Date.now();
      return stats;
    } catch (err) {
      return null;
    }
  })();
  return statsPreloadPromise;
};

const Overview = () => {
  const [stats, setStats] = useState(
    statsCache || {
      totalProjects: 0,
      completedTasks: 0,
      inProgressTasks: 0,
      overdueTasks: 0,
    }
  );
  const [loading, setLoading] = useState(!statsCache);
  const [error, setError] = useState(null);

  useEffect(() => {
    let ignore = false;
    const fetchStats = async () => {
      const now = Date.now();
      if (statsCache && now - statsCacheTimestamp < STATS_CACHE_DURATION) {
        setStats(statsCache);
        setLoading(false);
        // Preload in background
        preloadStats().then((newStats) => {
          if (!ignore && newStats) {
            setStats(newStats);
            setLoading(false);
          }
        });
        return;
      }
      setLoading(!statsCache); // Only show skeleton if no cache
      setError(null);
      try {
        const newStats = await preloadStats();
        if (!ignore && newStats) {
          setStats(newStats);
          setLoading(false);
        }
      } catch (err) {
        if (!ignore) {
          setError(err.message || "Lỗi không xác định");
          setLoading(false);
        }
      }
    };
    fetchStats();
    return () => {
      ignore = true;
    };
  }, []);

  useEffect(() => {
    preloadStats();
  }, []);

  const cards = [
    {
      title: "Tổng quan dự án",
      value: stats.totalProjects,
      desc: "+2 từ tháng trước",
      icon: fileText,
      iconColor: "#222",
    },
    {
      title: "Công việc hoàn thành",
      value: stats.completedTasks,
      desc: "12% so với tuần trước",
      icon: complete,
      iconColor: "#22c55e",
    },
    {
      title: "Đang thực hiện",
      value: stats.inProgressTasks,
      desc: "8 công việc được ưu tiên cao",
      icon: inprogress,
      iconColor: "#6366f1",
    },
    {
      title: "Trễ hạn",
      value: stats.overdueTasks,
      desc: "cần xử ngay",
      icon: overdue,
      iconColor: "#ef4444",
    },
  ];

  if (error) return <div style={{ color: "red" }}>Lỗi: {error}</div>;

  return (
    <div style={{ display: "flex", gap: 24 }}>
      {cards.map((card, idx) => (
        <div
          key={idx}
          style={{
            flex: 1,
            background: "#fff",
            borderRadius: 12,
            padding: 24,
            boxShadow: "0 1px 4px #f0f0f0",
            minWidth: 200,
          }}
        >
          <div
            style={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              marginBottom: 8,
            }}
          >
            <span style={{ fontWeight: 600 }}>{card.title}</span>
            <img
              src={card.icon}
              alt="icon"
              style={{ width: 22, height: 22, color: card.iconColor }}
            />
          </div>
          <div style={{ fontSize: 32, fontWeight: 700, marginBottom: 4, minHeight: 38 }}>
            {loading ? (
              <div style={{ width: 60, height: 32, background: '#f0f0f0', borderRadius: 6, animation: 'pulse 1.5s ease-in-out infinite' }}></div>
            ) : (
              card.value
            )}
          </div>
          <div style={{ color: "#888", fontSize: 15, minHeight: 20 }}>
            {loading ? (
              <div style={{ width: 80, height: 16, background: '#f0f0f0', borderRadius: 4, animation: 'pulse 1.5s ease-in-out infinite' }}></div>
            ) : (
              card.desc
            )}
          </div>
        </div>
      ))}
      <style>{`
        @keyframes pulse {
          0% { opacity: 1; }
          50% { opacity: 0.5; }
          100% { opacity: 1; }
        }
      `}</style>
    </div>
  );
};

export default Overview;
export const initialTasks = [
  // Đang chờ
  {
    id: "TSK-01",
    title: "<PERSON><PERSON><PERSON>ia<PERSON> diện trang chủ dự án",
    priority: "<PERSON><PERSON><PERSON> Thường",
    status: "<PERSON>ang chờ",
    created: "19/6/2025",
    due: "19/8/2025",
    members: ["🧑‍💻", "👩‍💻"],
    comments: 6,
  },
  {
    id: "TSK-02",
    title: "Thiết kế logo",
    priority: "Trung Bình",
    status: "Đang chờ",
    created: "20/6/2025",
    due: "25/6/2025",
    members: ["👨‍💻"],
    comments: 2,
  },
  {
    id: "TSK-03",
    title: "<PERSON><PERSON><PERSON> bị tài liệu họp",
    priority: "<PERSON>",
    status: "Đang chờ",
    created: "18/6/2025",
    due: "21/6/2025",
    members: ["👩‍💻"],
    comments: 1,
  },
  // Đang triển khai
  {
    id: "TSK-04",
    title: "<PERSON><PERSON><PERSON> tr<PERSON><PERSON> chức năng đăng nhập",
    priority: "<PERSON><PERSON><PERSON>",
    status: "<PERSON>ang triển khai",
    created: "17/6/2025",
    due: "22/6/2025",
    members: ["🧑‍💻"],
    comments: 3,
  },
  {
    id: "TSK-05",
    title: "Tối ưu giao diện mobile",
    priority: "Trung Bình",
    status: "Đang triển khai",
    created: "16/6/2025",
    due: "23/6/2025",
    members: ["👨‍💻", "👩‍💻"],
    comments: 4,
  },
  {
    id: "TSK-06",
    title: "Viết tài liệu hướng dẫn sử dụng",
    priority: "Cao",
    status: "Đang triển khai",
    created: "15/6/2025",
    due: "24/6/2025",
    members: ["👩‍💻"],
    comments: 2,
  },
  // Hoàn thành
  {
    id: "TSK-07",
    title: "Kiểm thử chức năng đăng ký",
    priority: "Bình Thường",
    status: "Hoàn thành",
    created: "10/6/2025",
    due: "15/6/2025",
    members: ["🧑‍💻"],
    comments: 1,
  },
  {
    id: "TSK-08",
    title: "Triển khai lên server",
    priority: "Trung Bình",
    status: "Hoàn thành",
    created: "12/6/2025",
    due: "16/6/2025",
    members: ["👨‍💻"],
    comments: 2,
  },
  {
    id: "TSK-09",
    title: "Hoàn thiện báo cáo cuối kỳ",
    priority: "Cao",
    status: "Hoàn thành",
    created: "11/6/2025",
    due: "17/6/2025",
    members: ["👩‍💻", "🧑‍💻"],
    comments: 5,
  },
  // Xem xét
  {
    id: "TSK-10",
    title: "Đánh giá hiệu suất hệ thống",
    priority: "Bình Thường",
    status: "Xem xét",
    created: "13/6/2025",
    due: "20/6/2025",
    members: ["👨‍💻"],
    comments: 2,
  },
  {
    id: "TSK-11",
    title: "Kiểm tra bảo mật",
    priority: "Trung Bình",
    status: "Xem xét",
    created: "14/6/2025",
    due: "21/6/2025",
    members: ["👩‍💻"],
    comments: 3,
  },
  {
    id: "TSK-12",
    title: "Phản hồi khách hàng",
    priority: "Cao",
    status: "Xem xét",
    created: "15/6/2025",
    due: "22/6/2025",
    members: ["🧑‍💻", "👨‍💻"],
    comments: 4,
  },
];

// Lưu và lấy kanban tasks theo từng projectId
export function getKanbanTasksByProjectId(projectId) {
  if (!projectId) return initialTasks;
  const key = `kanban_tasks_project_${projectId}`;
  const stored = localStorage.getItem(key);
  if (stored) {
    try {
      return JSON.parse(stored);
    } catch {
      return [];
    }
  }
  // Nếu là project mặc định (ví dụ SDTC), trả về tasks mẫu
  if (projectId === '1' || projectId === 1) return initialTasks;
  return [];
}

export function saveKanbanTasksByProjectId(projectId, taskList) {
  if (!projectId) return;
  const key = `kanban_tasks_project_${projectId}`;
  localStorage.setItem(key, JSON.stringify(taskList));
}

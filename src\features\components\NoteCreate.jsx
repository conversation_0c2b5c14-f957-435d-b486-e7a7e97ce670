import React, { useState, useRef, useEffect } from 'react';
import '../../styles/CreateNote.css';
import closeLoc from '../../assets/closeLoc.svg';
import { validateNoteForm } from '../../utils/validation';
import { showToast } from '../../utils/toastUtils';

const CreateNote = ({ onClose, onCreate }) => {
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [errors, setErrors] = useState({ title: '', content: '' });
  const modalRef = useRef(null);

  const validate = () => {
    const validationErrors = validateNoteForm({ title, content });
    setErrors(validationErrors);
    return !validationErrors.title && !validationErrors.content;
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (validate()) {
      onCreate && onCreate({ title, content });
      setTitle('');
      setContent('');
      onClose && onClose();
    }
  };

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (modalRef.current && !modalRef.current.contains(event.target)) {
        onClose && onClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [onClose]);

  return (
    <div className="create-note-modal">
      <form onSubmit={handleSubmit} className="create-note-form" ref={modalRef}>
        <div className="create-note-header">
          <h2 className="create-note-title">Tạo ghi chú mới</h2>
          <button type="button" onClick={onClose} className="create-note-close">
            <img src={closeLoc} alt="Close" />
          </button>
        </div>
        <div className="create-note-input-container">
          <input
            type="text"
            placeholder="Tiêu đề"
            value={title}
            onChange={e => setTitle(e.target.value)}
            className={`create-note-input ${errors.title ? 'create-note-input-error' : ''}`}
          />
          {errors.title && <div className="create-note-error-message">{errors.title}</div>}
        </div>
        <div className="create-note-textarea-container">
          <textarea
            placeholder="Nội dung ghi chú"
            value={content}
            onChange={e => setContent(e.target.value)}
            rows={5}
            className={`create-note-textarea ${errors.content ? 'create-note-input-error' : ''}`}
          />
          {errors.content && <div className="create-note-error-message">{errors.content}</div>}
        </div>
        <div className="create-note-buttons">
          <button type="button" className="create-note-cancel" onClick={onClose}>
            Hủy
          </button>
          <button type="submit" className="create-note-submit">
            Tạo
          </button>
        </div>
      </form>
    </div>
  );
};

export default CreateNote;

@import url('../index.css');
/* <PERSON><PERSON> cục thẻ quy trình giống ảnh mẫu */
.procedure-card {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  background: #fff;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 20px;
  box-shadow: 0 1px 4px rgba(0,0,0,0.04);
  cursor: pointer;
}
.procedure-card-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  width: 100%;
  gap: 32px;
}
.procedure-col {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}
.procedure-col-left {
  min-width: 220px;
  flex: 1.2;
}
.procedure-col-center {
  min-width: 220px;
  flex: 1.2;
}
.procedure-col-right {
  min-width: 180px;
  flex: 1;
  align-items: flex-end;
}
.procedure-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-width: 350px;
}
.procedure-row {
  display: flex;
  gap: 40px;
  font-size: 15px;
  margin-bottom: 8px;
}
.procedure-label {
  color: #888;
  font-size: 13px;
  margin-bottom: 2px;
  margin-top: 0;
}
.procedure-value {
  font-weight: 500;
  font-size: 15px;
  color: #222;
}
.procedure-title {
  font-weight: 600;
  font-size: 17px;
  color: #222;
}
.procedure-id {
  color: #888;
  font-size: 13px;
  margin-bottom: 0;
}
.procedure-desc {
  color: #222;
  font-size: 15px;
  margin-top: 0;
}
.procedure-status {
  min-width: 180px;
  text-align: right;
}
.procedure-status-row {
  display: flex;
  align-items: center;
  gap: 16px;
  justify-content: flex-end;
}
.procedure-priority, .procedure-done {
  display: flex;
  align-items: center;
  gap: 4px;
}
.procedure-priority-icon, .procedure-done-icon {
  width: 18px;
  height: 18px;
  margin-bottom: -2px;
}
.procedure-priority-text.high { font-weight: 500; font-size: 15px; }
.procedure-priority-text.critical { font-weight: 500; font-size: 15px; }
.procedure-priority-text.medium { font-weight: 500; font-size: 15px; }
.procedure-priority-text.low { font-weight: 500; font-size: 15px; }

.procedure-done-text.completed { font-weight: 500; font-size: 15px; }
.procedure-done-text.overdue { font-weight: 500; font-size: 15px; }
.procedure-done-text.pending { font-weight: 500; font-size: 15px; }
.procedure-done-text.in_progress { font-weight: 500; font-size: 15px; }
.procedure-done-text.review { font-weight: 500; font-size: 15px; }
.procedure-done-text.in-progress, 
.procedure-done-text.waiting, 
.procedure-done-text.consider { color: #888; font-weight: 500; font-size: 15px; }

.procedure-progress-label {
  margin-top: 24px;
  font-size: 15px;
  color: #888;
  text-align: right;
}
.procedure-progress-bar {
  display: flex;
  align-items: center;
  gap: 8px;
  justify-content: flex-end;
}
.procedure-bar-bg {
  width: 120px;
  height: 8px;
  background: #eee;
  border-radius: 4px;
  overflow: hidden;
}
.procedure-bar-fill {
  height: 8px;
  background: #3498db;
}
.procedure-progress-value {
  font-weight: 500;
  font-size: 15px;
  color: #222;
}

/* Responsive */
@media (max-width: 900px) {
  .procedure-card, .procedure-card-row {
    flex-direction: column;
    gap: 16px;
    text-align: left;
  }
  .procedure-status {
    text-align: left;
    min-width: unset;
  }
  .procedure-card-row, .procedure-col {
    flex-direction: column;
    gap: 16px;
    text-align: left;
    align-items: flex-start;
  }
  .procedure-col-right {
    align-items: flex-start;
    text-align: left;
  }
}

/* Loading states */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 0;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

.loading-text {
  color: #666;
  font-size: 16px;
  margin-bottom: 24px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Skeleton loading */
.procedure-card.skeleton {
  cursor: default;
  pointer-events: none;
  opacity: 0.8;
}

.skeleton-title {
  height: 20px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 4px;
  margin-bottom: 8px;
  width: 90%;
}

.skeleton-text {
  height: 16px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 4px;
  margin-bottom: 6px;
  width: 80%;
}

.skeleton-text:nth-child(2) {
  width: 60%;
}

.skeleton-text:nth-child(3) {
  width: 70%;
}

.skeleton-text:nth-child(4) {
  width: 85%;
}

.skeleton-progress {
  height: 8px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 4px;
  width: 120px;
  margin-top: 8px;
}

/* Avatar skeleton for team members */
.skeleton-avatars {
  display: flex;
  gap: 8px;
  margin-top: 8px;
}

.skeleton-avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

/* Error container */
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

/* Fade in animation for loaded data */
.procedure-card {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Removed DashboardSidebar and DashboardTopbar imports as they're now handled by DashboardLayout
import React, { useState, useEffect, useMemo } from "react";
import StatisticalCard from "../../components/StatisticalCard";
import HighIcon from "../../../assets/High.svg";
import MediumIcon from "../../../assets/Medium.svg";
import NormalIcon from "../../../assets/Normal.svg";
  // Helper to get priority icon
  const getPriorityIcon = (priority) => {
    const p = String(priority || '').toLowerCase();
    switch (p) {
      case 'high':
        return HighIcon;
      case 'medium':
        return MediumIcon;
      case 'low':
      case 'normal':
      default:
        return NormalIcon;
    }
  };
import checkIcon from "../../../assets/complete.svg";
import chartIcon from "../../../assets/chart-column-decreasing.svg";
import userGroupIcon from "../../../assets/users.svg";
import fileIcon from "../../../assets/file-text.svg";
import filterIcon from "../../../assets/filter.svg";
import downloadIcon from "../../../assets/download.svg";
import DetailJob from "../../components/JobDetail";
import { getAllProjects } from "../../../api/projectManagement";
import { getProjectTasks, transformTaskListData } from "../../../api/taskManagement";
import { getProjectMembers } from "../../../api/userManagement";
import * as XLSX from "xlsx";
import jsPDF from "jspdf";
import { saveAs } from "file-saver";
import "../../../styles/Procedure.css"
import waitingIcon from "../../../assets/waiting.svg";
import triangleAlertIcon from "../../../assets/triangle-alert.svg";
import deploymentIcon from "../../../assets/deployment.svg";
import considerIcon from "../../../assets/consider.svg";
import completeIcon from "../../../assets/complete.svg";

// Cache để tránh gọi API nhiều lần
let proceduresCache = null;
let cacheTimestamp = 0;
const CACHE_DURATION = 10000; // Giảm xuống 10 giây để load nhanh hơn

// Preload data for instant loading
let preloadPromise = null;

const ProcedureList = () => {
  const [procedures, setProcedures] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedProcedure, setSelectedProcedure] = useState(null);

  // Memoize procedures to prevent unnecessary re-renders
  const memoizedProcedures = useMemo(() => procedures, [procedures]);

  // Preload function
  const preloadData = async () => {
    if (preloadPromise) return preloadPromise;
    
    preloadPromise = (async () => {
      try {
        const projectsRes = await getAllProjects();
        const projects = projectsRes.data || [];
        
        if (projects.length === 0) return [];

        const projectDataPromises = projects.map(async (project) => {
          const projectId = project.id || project._id;
          try {
            const [tasksRes, membersRes] = await Promise.all([
              getProjectTasks(projectId),
              getProjectMembers(projectId).catch(() => ({ data: [] }))
            ]);
            const projectTasks = transformTaskListData(tasksRes.data || []);
            return projectTasks.map(task => ({
              ...task,
              projectName: project.name,
              projectId: projectId
            }));
          } catch (err) {
            return [];
          }
        });

        const allProjectData = await Promise.all(projectDataPromises);
        const allTasks = allProjectData.flat();
        
        proceduresCache = allTasks;
        cacheTimestamp = Date.now();
        
        return allTasks;
      } catch (err) {
        return [];
      }
    })();
    
    return preloadPromise;
  };

  useEffect(() => {
    async function fetchProcedures() {
      // Kiểm tra cache trước
      const now = Date.now();
      if (proceduresCache && (now - cacheTimestamp) < CACHE_DURATION) {
        setProcedures(proceduresCache);
        setLoading(false);
        return;
      }
      // Nếu không có cache, mới set loading=true
      setLoading(true);
      setError(null);
      
      try {
        // Use preloaded data if available
        let allTasks = [];
        if (preloadPromise) {
          allTasks = await preloadPromise;
          setProcedures(allTasks);
        } else {
          // Fetch projects first
          const projectsRes = await getAllProjects();
          const projects = projectsRes.data || [];
          
          if (projects.length === 0) {
            setProcedures([]);
            setLoading(false);
            return;
          }

          // Fetch all project data concurrently
          const projectDataPromises = projects.map(async (project) => {
            const projectId = project.id || project._id;
            
            try {
              // Fetch tasks and members in parallel for each project
              const [tasksRes, membersRes] = await Promise.all([
                getProjectTasks(projectId),
                getProjectMembers(projectId).catch(() => ({ data: [] }))
              ]);

              const projectTasks = transformTaskListData(tasksRes.data || []);
              const members = membersRes.data || [];

              return projectTasks.map(task => ({
                ...task,
                projectName: project.name,
                projectId: projectId
              }));
            } catch (err) {
              console.warn(`Error fetching data for project ${projectId}:`, err);
              return [];
            }
          });

          // Wait for all data to be fetched
          const allProjectData = await Promise.all(projectDataPromises);
          allTasks = allProjectData.flat();
          setProcedures(allTasks);
        }
        
        // Lưu vào cache
        proceduresCache = allTasks;
        cacheTimestamp = now;
        
      } catch (err) {
        console.warn('Error fetching procedures:', err);
        setProcedures([]);
        setError('Lỗi khi tải dữ liệu công việc');
      } finally {
        setLoading(false);
      }
    }
    
    fetchProcedures();
  }, [window.location.pathname]);

  // Start preloading when component mounts
  useEffect(() => {
    preloadData();
  }, []);

  const handleFilter = () => {
    // ...
  };
  const handleExport = (type) => {
    if (!memoizedProcedures || memoizedProcedures.length === 0) {
      alert('Không có dữ liệu để xuất!');
      return;
    }

    if (type === "excel") {
      const exportData = memoizedProcedures.map(({ projectId, projectName, id, name, startDate, dueDate, status, assignee, progress, priority, description }) => ({
        "Mã dự án": projectId,
        "Tên dự án": projectName,
        "Mã công việc": id,
        "Tên công việc": name,
        "Thời gian bắt đầu": startDate,
        "Thời gian kết thúc": dueDate,
        "Trạng thái":
          status === 'completed' ? 'Hoàn thành' :
          status === 'in-progress' ? 'Đang triển khai' :
          status === 'in_progress' ? 'Đang triển khai' :
          status === 'waiting' ? 'Đang chờ' :
          status === 'pending' ? 'Đang chờ' :
          status === 'overdue' ? 'Quá hạn' :
          status === 'review' ? 'Đang xem xét' :
          status === 'consider' ? 'Đang xem xét' : status,
        "Thành viên": assignee ? assignee.map(u => u.name).join(", ") : "",
        "Tiến độ": progress || (status === 'completed' ? 100 : status === 'in-progress' ? 50 : 0),
        "Độ ưu tiên": priority,
        "Mô tả": description
      }));
      const ws = XLSX.utils.json_to_sheet(exportData);
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, "QuyTrinhCongViec");
      const wbout = XLSX.write(wb, { bookType: "xlsx", type: "array" });
      saveAs(new Blob([wbout], { type: "application/octet-stream" }), "quy_trinh_cong_viec.xlsx");
    } else if (type === "pdf") {
      const doc = new jsPDF();
      doc.text("Quy trình công việc", 10, 10);
      let y = 20;
      memoizedProcedures.forEach((item, idx) => {
        doc.text(`${idx + 1}. ${item.name} (${item.id})`, 10, y);
        doc.text(`Dự án: ${item.projectName} (${item.projectId})`, 10, y + 8);
        doc.text(`Thời gian: ${item.startDate} - ${item.dueDate}`, 10, y + 16);
        doc.text(`Trạng thái: ${item.status === 'completed' ? 'Hoàn thành' : item.status === 'in-progress' ? 'Đang triển khai' : item.status === 'in_progress' ? 'Đang triển khai' : item.status === 'waiting' ? 'Đang chờ' : item.status === 'pending' ? 'Đang chờ' : item.status === 'overdue' ? 'Quá hạn' : item.status === 'review' ? 'Đang xem xét' : item.status === 'consider' ? 'Đang xem xét' : item.status} | Ưu tiên: ${item.priority} | Thành viên: ${(item.assignee || []).map(u => u.name).join(", ")}` , 10, y + 24);
        doc.text(`Tiến độ: ${item.progress || (item.status === 'completed' ? 100 : item.status === 'in-progress' ? 50 : 0)}%`, 10, y + 32);
        doc.text(`Mô tả: ${item.description || ''}`, 10, y + 40);
        y += 52;
        if (y > 270) { doc.addPage(); y = 20; }
      });
      doc.save("quy_trinh_cong_viec.pdf");
    }
  };
  const handleCloseDetail = () => setSelectedProcedure(null);

  // Helper format date
  const formatDate = (dateStr) => {
    if (!dateStr) return 'N/A';
    
    // If it's already in DD/MM/YYYY format, return as is
    if (typeof dateStr === 'string' && /^\d{1,2}\/\d{1,2}\/\d{4}$/.test(dateStr)) {
      return dateStr;
    }
    
    const d = new Date(dateStr);
    if (isNaN(d)) return dateStr;
    
    // Format to DD/MM/YYYY
    const day = d.getDate();
    const month = d.getMonth() + 1;
    const year = d.getFullYear();
    
    return `${day}/${month}/${year}`;
  };
  // Helper format mã dự án/công việc
  const formatCode = (id, type = 'DA') => {
    if (!id) return '';
    const str = String(id);
    return type + str.slice(-6);
  };

  // Helper to get status icon
  const getStatusIcon = (status) => {
    const statusLower = String(status || '').toLowerCase();
    switch (statusLower) {
      case "in_progress":
      case "in-progress":
      case "đang triển khai":
        return deploymentIcon;
      case "completed":
      case "hoàn thành":
        return completeIcon;
      case "pending":
      case "waiting":
      case "đang chờ":
        return waitingIcon;
      case "overdue":
      case "quá hạn":
        return triangleAlertIcon;
      case "review":
      case "consider":
      case "đang xem xét":
        return considerIcon;
      default:
        return waitingIcon;
    }
  };

  // Hàm lấy icon mức độ ưu tiên
  const getPriorityIcon = (priority) => {
    const p = String(priority).toLowerCase();
    switch (p) {
      case "high":
      case "cao":
        return HighIcon; // đỏ
      case "critical":
      case "urgent":
      case "khẩn cấp":
      case "khan cap":
        return HighIcon; // đỏ
      case "medium":
      case "trung bình":
      case "trung binh":
        return MediumIcon; // cam
      case "low":
      case "thấp":
      case "thap":
        return NormalIcon; // xanh lá
      default:
        return NormalIcon;
    }
  };

  // Hàm lấy text mức độ ưu tiên
  const getPriorityText = (priority) => {
    const p = String(priority).toLowerCase();
    switch (p) {
      case "high":
      case "cao":
        return "Cao";
      case "critical":
      case "urgent":
      case "khẩn cấp":
      case "khan cap":
        return "Khẩn cấp";
      case "medium":
      case "trung bình":
      case "trung binh":
        return "Trung bình";
      case "low":
      case "thấp":
      case "thap":
        return "Thấp";
      default:
        return "Thấp";
    }
  };

  return (
    <>
      <div className="statistical-list-container">
        <StatisticalCard
          fileIcon={fileIcon}
          chartIcon={chartIcon}
          userGroupIcon={userGroupIcon}
          filterIcon={filterIcon}
          downloadIcon={downloadIcon}
          onFilter={handleFilter}
          onExport={handleExport}
        />
        <div style={{ marginTop: 24 }}>
          {loading && memoizedProcedures.length === 0 ? (
            <div>
              {/* Loading skeleton - only show when no data available */}
              {[1, 2, 3, 4, 5].map((idx) => (
                <div key={idx} className="procedure-card skeleton">
                  <div className="procedure-card-row" style={{gap: 32}}>
                    <div className="procedure-col procedure-col-left">
                      <div className="skeleton-title"></div>
                      <div className="skeleton-text"></div>
                      <div className="skeleton-text"></div>
                      <div className="skeleton-avatars">
                        <div className="skeleton-avatar"></div>
                        <div className="skeleton-avatar"></div>
                        <div className="skeleton-avatar"></div>
                      </div>
                      <div className="skeleton-text"></div>
                    </div>
                    <div className="procedure-col procedure-col-center">
                      <div className="skeleton-title"></div>
                      <div className="skeleton-text"></div>
                      <div className="skeleton-text"></div>
                    </div>
                    <div className="procedure-col procedure-col-right">
                      <div className="skeleton-text"></div>
                      <div className="skeleton-progress"></div>
                    </div>
                  </div>
                </div>
              ))}
              {/* Loading indicator at bottom */}
              <div className="loading-container" style={{ padding: '20px 0' }}>
                <div className="loading-spinner"></div>
                <div className="loading-text">Đang tải dữ liệu...</div>
              </div>
            </div>
          ) : error ? (
            <div className="error-container">
              <div style={{ color: 'red', textAlign: 'center', padding: '20px' }}>
                <div>❌ {error}</div>
                <button
                  onClick={() => window.location.reload()}
                  style={{
                    marginTop: '10px',
                    padding: '8px 16px',
                    background: '#007bff',
                    color: 'white',
                    border: 'none',
                    borderRadius: '4px',
                    cursor: 'pointer'
                  }}
                >
                  Thử lại
                </button>
              </div>
            </div>
          ) : !loading && memoizedProcedures.length === 0 ? (
            <div style={{ textAlign: 'center', padding: '40px', color: '#666' }}>
              <div>📋 Không có dữ liệu công việc.</div>
            </div>
          ) : (
            <div>
              {/* Show real data immediately */}
              {memoizedProcedures.map((item, idx) => (
                <div
                  key={item.id || item._id || idx}
                  className="procedure-card"
                  onClick={() => setSelectedProcedure(item)}
                >
                  <div className="procedure-card-row" style={{gap: 32}}>
                    {/* Cột trái: Thông tin dự án, người thực hiện, mô tả */}
                    <div className="procedure-col procedure-col-left">
                      <div className="procedure-title">{item.projectName || 'Hệ thống quản lí bán hàng'}</div>
                      <div className="procedure-id">Mã dự án: {item.projectCode || item.project?.projectCode || 'PRJ-N/A'}</div>
                      <div className="procedure-label" style={{marginTop: 16}}>Người thực hiện</div>
                      <div className="procedure-value avatars">
                        {item.assignee && item.assignee.length > 0 ? (
                          item.assignee.map((u, idx) => (
                            <img
                              key={(u.id || u._id || u.name || idx) + idx}
                              src={u.avatar && u.avatar.trim() !== '' ? u.avatar : (u.user && u.user.avatar && u.user.avatar.trim() !== '' ? u.user.avatar : 'https://randomuser.me/api/portraits/men/1.jpg')}
                              alt={u.name || (u.user && u.user.name) || "Thành viên"}
                              style={{ width: 24, height: 24, borderRadius: "50%" }}
                              onError={e => { e.target.src = 'https://randomuser.me/api/portraits/men/1.jpg'; }}
                            />
                          ))
                        ) : (
                          <span style={{ color: '#888', fontStyle: 'italic' }}>Chưa phân công</span>
                        )}
                      </div>
                      <div className="procedure-label" style={{marginTop: 16}}>Mô tả công việc</div>
                      <div className="procedure-desc">
                        {item.description && item.description.length > 60
                          ? item.description.slice(0, 60) + '...'
                          : item.description || 'Không có mô tả'}
                      </div>
                    </div>
                    {/* Cột giữa: Thông tin công việc, thời gian thực hiện */}
                    <div className="procedure-col procedure-col-center">
                      <div className="procedure-title">{(item.name || item.title || 'Thiết kế giao diện người dùng').replace(/^TASK-[A-Z0-9]+ /, '')}</div>
                      <div className="procedure-id">Mã công việc: {formatCode(item.id, 'TSK-')}</div>
                      <div className="procedure-label" style={{marginTop: 16}}>Thời gian thực hiện</div>
                      <div className="procedure-value">{formatDate(item.startDate)} - {formatDate(item.dueDate)}</div>
                    </div>
                    {/* Cột phải: Trạng thái, tiến độ */}
                    <div className="procedure-col procedure-col-right">
                      <div className="procedure-status-row" style={{justifyContent: 'flex-end', gap: 16}}>
                        <span className="procedure-priority">
                          <img src={getPriorityIcon(item.priority)} alt="priority" className="procedure-priority-icon" />
                          <span className={`procedure-priority-text ${item.priority || 'normal'}`}>{
                            (item.priority || 'normal') === 'high'
                              ? 'Cao'
                              : (item.priority || 'normal') === 'medium'
                              ? 'Trung bình'
                              : 'Thấp'
                          }</span>
                        </span>
                        <span className="procedure-done">
                          <img src={getStatusIcon(item.status)} alt="status" className="procedure-done-icon" />
                          <span className={`procedure-done-text ${item.status || 'waiting'}`}>
                            {(() => {
                              const status = String(item.status || '').toLowerCase();
                              switch (status) {
                                case 'completed':
                                case 'hoàn thành':
                                  return 'Hoàn thành';
                                case 'in-progress':
                                case 'in_progress':
                                case 'đang triển khai':
                                  return 'Đang triển khai';
                                case 'waiting':
                                case 'pending':
                                case 'đang chờ':
                                  return 'Đang chờ';
                                case 'overdue':
                                case 'quá hạn':
                                  return 'Quá hạn';
                                case 'review':
                                case 'consider':
                                case 'đang xem xét':
                                  return 'Đang xem xét';
                                default:
                                  return 'Đang chờ';
                              }
                            })()}
                          </span>
                        </span>
                      </div>
                      <div className="procedure-progress-label" style={{textAlign: 'right', marginTop: 24}}>Tiến độ</div>
                      <div className="procedure-progress-bar" style={{justifyContent: 'flex-end'}}>
                        <div className="procedure-bar-bg">
                          <div className="procedure-bar-fill" style={{ width: `${item.progress || (item.status === 'completed' ? 100 : item.status === 'in-progress' ? 50 : 0)}%` }}></div>
                        </div>
                        <span className="procedure-progress-value">{item.progress || (item.status === 'completed' ? 100 : item.status === 'in-progress' ? 50 : 0)}%</span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
              {/* Show loading indicator at bottom if still loading */}
              {loading && (
                <div className="loading-container" style={{ padding: '20px 0', borderTop: '1px solid #eee', marginTop: '20px' }}>
                  <div className="loading-spinner"></div>
                  <div className="loading-text">Đang tải thêm dữ liệu...</div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
      {selectedProcedure && (
        <DetailJob
          task={{
            ...selectedProcedure,
            creator: selectedProcedure.creator || { name: 'Không xác định', avatar: null },
            assignee: selectedProcedure.assignee || [],
            priority: selectedProcedure.priority || 'low',
            status: (() => {
              const status = String(selectedProcedure.status || '').toLowerCase();
              switch (status) {
                case 'completed':
                case 'hoàn thành':
                  return 'Hoàn thành';
                case 'in-progress':
                case 'in_progress':
                case 'đang triển khai':
                  return 'Đang triển khai';
                case 'waiting':
                case 'pending':
                case 'đang chờ':
                  return 'Đang chờ';
                case 'overdue':
                case 'quá hạn':
                  return 'Quá hạn';
                case 'review':
                case 'consider':
                case 'đang xem xét':
                  return 'Đang xem xét';
                default:
                  return 'Đang chờ';
              }
            })(),
            description: selectedProcedure.description || 'Không có mô tả',
            progress: selectedProcedure.progress || 0
          }}
          onClose={handleCloseDetail}
          hideActivityAndComment={true}
        />
      )}
    </>
  );
};

export default ProcedureList;

@import url('../index.css');
.update-note-modal {
  background: rgba(0, 0, 0, 0.04);
  position: fixed;
  top: 0; left: 0; width: 100vw; height: 100vh;
  display: flex; align-items: center; justify-content: center;
  z-index: 1000;
}
.update-note-form {
  background: #fff;
  border-radius: 14px;
  padding: 20px;
  width: 500px;
  box-shadow: 0 4px 24px rgba(0,0,0,0.10);
  position: relative;
}
.update-note-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 18px;
}

.update-note-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #666;
}

.update-note-close {
  background: none;
  border: none;
  cursor: pointer;
  transition: opacity 0.2s;
  padding: 0;
  margin-top: -4px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.update-note-close img {
  width: 20px;
  height: 20px;
}

.update-note-close:hover {
  opacity: 0.7;
}

.update-note-input-container,
.update-note-textarea-container {
  position: relative;
  margin-bottom: 24px;
  height: auto;
}

.update-note-input {
  width: 100%;
  padding: 10px 14px;
  border-radius: 8px;
  border: 1px solid #e0e3e7;
  font-size: 14px !important;
  color: #000;
  background: #fff;
  transition: border 0.2s;
  box-sizing: border-box;
  margin-bottom: 0;
}
.update-note-input:focus, .update-note-textarea:focus {
  border: 1.5px solid #1976d2;
  outline: none;
}
.update-note-textarea {
  width: 100%;
  padding: 12px 14px;
  border-radius: 8px;
  border: 1px solid #e0e3e7;
  font-size: 14px;
  font-family: 'Be Vietnam Pro', Arial, sans-serif;
  resize: none;
  color: #000;
  background: #fff;
  min-height: 90px;
  margin-bottom: 0;
  transition: border 0.2s;
  box-sizing: border-box;
}
.update-note-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 22px;
}

.update-note-delete {
  background: #fff;
  color: #3B86F6;
  border: none;
  border-radius: 8px;
  padding: 10px 18px;
  font-size: 15px;
  font-weight: 500;
  cursor: pointer;
  text-decoration: none;
  transition: background 0.2s, color 0.2s;
}

.update-note-delete:hover {
  background: #bbdefb;
  color: red;
}

.update-note-submit {
  background: #3B86FF;
  color: #fff;
  border: none;
  border-radius: 8px;
  padding: 10px 22px;
  font-size: 15px;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.2s;
}

.update-note-submit:hover {
  background: #1251a3;
}

.update-note-input-error {
  border: 1px solid #f44336 !important;
}

.update-note-error-message {
  color: #f44336;
  font-size: 12px;
  margin-top: 4px;
  font-weight: 500;
  height: 16px;
  display: block;
  position: absolute;
}

import { useState, useRef, useEffect } from 'react';
import JobNoti from '../pages/Notifical/JobNoti';
import SysNoti from '../pages/Notifical/SysNoti';
import CmtNoti from '../pages/Notifical/CmtNoti';
import ProjectNoti from '../pages/Notifical/ProjectNoti';
import { markAllJobNotificationsAsRead, markAllJobNotificationsAsUnread } from '../../storage/jobNotificationData';
import { markAllSystemNotificationsAsRead, markAllSystemNotificationsAsUnread } from '../../storage/systemNotificationData';
import { markAllCommentNotificationsAsRead, markAllCommentNotificationsAsUnread } from '../../storage/commentNotificationData';
import { markAllProjectNotificationsAsRead, markAllProjectNotificationsAsUnread } from '../../storage/projectNotificationData';
import '../../styles/Notification.css';

const Notification = () => {
  const [activeTab, setActiveTab] = useState('job');
  const [globalFilter, setGlobalFilter] = useState('all');
  const [showFilterDropdown, setShowFilterDropdown] = useState(false);
  const [showMoreActionsDropdown, setShowMoreActionsDropdown] = useState(false);
  const [markAllAsReadChecked, setMarkAllAsReadChecked] = useState(false);
  const [markAllAsUnreadChecked, setMarkAllAsUnreadChecked] = useState(false);
  const [refreshKey, setRefreshKey] = useState(0);
  const filterRef = useRef(null);
  const moreActionsRef = useRef(null);
  const filterBtnRef = useRef(null);
  const moreActionsBtnRef = useRef(null);
  const filterDropdownRef = useRef(null);

  const tabs = [
    { id: 'job', label: 'Công việc', component: JobNoti },
    { id: 'system', label: 'Hệ thống', component: SysNoti },
    { id: 'comment', label: 'Bình luận', component: CmtNoti },
    { id: 'project', label: 'Dự án', component: ProjectNoti }
  ];

  const filterOptions = [
    { id: 'all', label: 'Tất cả' },
    { id: 'unread', label: 'Chưa đọc' },
    { id: 'read', label: 'Đã đọc' }
  ];

  // Đặt vị trí cho dropdown khi nó hiển thị
  useEffect(() => {
    if (showFilterDropdown && filterBtnRef.current && filterDropdownRef.current) {
      const buttonRect = filterBtnRef.current.getBoundingClientRect();
      const dropdownWidth = filterDropdownRef.current.offsetWidth;
      
      // Đặt vị trí dropdown ở giữa button
      const leftPosition = buttonRect.width / 2 - dropdownWidth / 2;
      filterDropdownRef.current.style.left = `${leftPosition}px`;
    }
  }, [showFilterDropdown]);

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event) {
      // Only close filter dropdown if click is outside both the dropdown and its trigger button
      if (
        filterRef.current && 
        !filterRef.current.contains(event.target) && 
        filterBtnRef.current && 
        !filterBtnRef.current.contains(event.target)
      ) {
        setShowFilterDropdown(false);
      }
      
      // Only close more actions dropdown if click is outside both the dropdown and its trigger button
      if (
        moreActionsRef.current && 
        !moreActionsRef.current.contains(event.target) && 
        moreActionsBtnRef.current && 
        !moreActionsBtnRef.current.contains(event.target)
      ) {
        setShowMoreActionsDropdown(false);
      }
    }
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleFilterSelect = (filterId, event) => {
    event.stopPropagation();
    setGlobalFilter(filterId);
    setShowFilterDropdown(false);
  };

  const toggleFilterDropdown = (event) => {
    event.stopPropagation();
    setShowFilterDropdown(prev => !prev);
    // Close other dropdown if open
    if (showMoreActionsDropdown) {
      setShowMoreActionsDropdown(false);
    }
  };

  const toggleMoreActionsDropdown = (event) => {
    event.stopPropagation();
    setShowMoreActionsDropdown(prev => !prev);
    // Close other dropdown if open
    if (showFilterDropdown) {
      setShowFilterDropdown(false);
    }
  };

  const getFilterLabel = () => {
    const filter = filterOptions.find(f => f.id === globalFilter);
    return filter ? filter.label : 'Tất cả';
  };

  const handleMarkAllAsRead = (event) => {
    event.stopPropagation();
    setMarkAllAsReadChecked(!markAllAsReadChecked);
    // Nếu tích "đã đọc tất cả" thì bỏ tích "chưa đọc tất cả"
    if (!markAllAsReadChecked) {
      setMarkAllAsUnreadChecked(false);
      // Đánh dấu tất cả thông báo đã đọc
      markAllJobNotificationsAsRead();
      markAllSystemNotificationsAsRead();
      markAllCommentNotificationsAsRead();
      markAllProjectNotificationsAsRead();
      // Trigger re-render
      setRefreshKey(prev => prev + 1);
    }
  };

  const handleMarkAllAsUnread = (event) => {
    event.stopPropagation();
    setMarkAllAsUnreadChecked(!markAllAsUnreadChecked);
    // Nếu tích "chưa đọc tất cả" thì bỏ tích "đã đọc tất cả"
    if (!markAllAsUnreadChecked) {
      setMarkAllAsReadChecked(false);
      // Đánh dấu tất cả thông báo chưa đọc
      markAllJobNotificationsAsUnread();
      markAllSystemNotificationsAsUnread();
      markAllCommentNotificationsAsUnread();
      markAllProjectNotificationsAsUnread();
      // Trigger re-render
      setRefreshKey(prev => prev + 1);
    }
  };

  const handleTabChange = (tabId, event) => {
    event.stopPropagation();
    setActiveTab(tabId);
  };

  const renderActiveComponent = () => {
    const activeTabData = tabs.find(tab => tab.id === activeTab);
    if (activeTabData) {
      const Component = activeTabData.component;
      return <Component key={refreshKey} globalFilter={globalFilter} />;
    }
    return null;
  };

  return (
    <div className="notification-popup" onClick={(e) => e.stopPropagation()}>
      <div className="notification-popup-header">
        <div className="notification-popup-title">
          <h3>Hộp thư đến</h3>

          <div className="filter-dropdown-wrapper" ref={filterRef}>
            <button
              ref={filterBtnRef}
              className="filter-btn-popup"
              onClick={toggleFilterDropdown}
            >
              <span>{getFilterLabel()}</span>
              <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <polyline points="6,9 12,15 18,9"></polyline>
              </svg>
            </button>

            {showFilterDropdown && (
              <div ref={filterDropdownRef} className="filter-dropdown show filter-dropdown-centered">
                {filterOptions.map(option => (
                  <button
                    key={option.id}
                    className={`filter-option ${globalFilter === option.id ? 'active' : ''}`}
                    onClick={(e) => handleFilterSelect(option.id, e)}
                  >
                    {option.label}
                  </button>
                ))}
              </div>
            )}
          </div>

          <div className="notification-popup-actions">
            <div className="more-actions-dropdown-wrapper" ref={moreActionsRef}>
              <button
                ref={moreActionsBtnRef}
                className="more-actions-popup"
                onClick={toggleMoreActionsDropdown}
              >
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <circle cx="12" cy="12" r="1"></circle>
                  <circle cx="19" cy="12" r="1"></circle>
                  <circle cx="5" cy="12" r="1"></circle>
                </svg>
              </button>

              {showMoreActionsDropdown && (
                <div className="more-actions-dropdown show">
                  <button
                    className="more-actions-option"
                    onClick={handleMarkAllAsRead}
                  >
                    <span className="more-actions-option-text">Đánh dấu đã đọc tất cả</span>
                    <div className={`more-actions-option-checkbox ${markAllAsReadChecked ? 'checked' : ''}`}>
                      {markAllAsReadChecked && (
                        <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="3">
                          <path d="M20 6L9 17l-5-5"></path>
                        </svg>
                      )}
                    </div>
                  </button>
                  <button
                    className="more-actions-option"
                    onClick={handleMarkAllAsUnread}
                  >
                    <span className="more-actions-option-text">Đánh dấu chưa đọc tất cả</span>
                    <div className={`more-actions-option-checkbox ${markAllAsUnreadChecked ? 'checked' : ''}`}>
                      {markAllAsUnreadChecked && (
                        <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="3">
                          <path d="M20 6L9 17l-5-5"></path>
                        </svg>
                      )}
                    </div>
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="notification-popup-tabs">
          {tabs.map(tab => (
            <button
              key={tab.id}
              className={`tab-button-popup ${activeTab === tab.id ? 'active' : ''}`}
              onClick={(e) => handleTabChange(tab.id, e)}
            >
              {tab.label}
            </button>
          ))}
        </div>
      </div>

      <div className="notification-popup-content">
        {renderActiveComponent()}
      </div>
    </div>
  );
};

export default Notification;
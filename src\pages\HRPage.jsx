import React, { useState, useEffect } from "react";
import { useSearchParams } from "react-router-dom";
import HRLayout from '../features/components/HRLayout';
import ListUser from '../features/pages/HR/ListUser';
// Removed DashboardSidebar and DashboardTopbar imports as they're now handled by DashboardLayout
import { getAllUsers, createUser, updateUser, toggleUserStatus, transformUserData } from '../api/userManagement';
import { getAllDepartments } from '../api/departmentManagement';

// CSS cho loading animation
const loadingStyles = `
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

// Thêm styles vào head nếu chưa có
if (!document.querySelector('#loading-styles')) {
  const style = document.createElement('style');
  style.id = 'loading-styles';
  style.textContent = loadingStyles;
  document.head.appendChild(style);
}

const HRPage = () => {
  const [searchParams] = useSearchParams();
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [departmentMap, setDepartmentMap] = useState({});
  
  // Đọc department parameter từ URL để set initial filter
  const departmentFromUrl = searchParams.get('department');
  const [filter, setFilter] = useState({
    department: departmentFromUrl ? decodeURIComponent(departmentFromUrl) : 'Tất cả phòng ban',
    role: 'Tất cả chức vụ',
    status: 'Tất cả trạng thái',
    search: '',
  });

  // Refresh users sau khi có department mapping
  const refreshUsers = async () => {
    try {
      setLoading(true);
      const response = await getAllUsers({
        populate: 'department,departmentId',
        include: 'department'
      });

      if (response.success) {
        const transformedUsers = (response.data || []).map(backendUser => {
          const basicTransform = transformUserData(backendUser);

          // Nếu department chưa có tên, thử lookup từ mapping
          if (basicTransform.department === 'Chưa phân công') {
            const departmentId = backendUser.departmentId || backendUser.department;
            if (departmentId && departmentMap[departmentId]) {
              basicTransform.department = departmentMap[departmentId];
            }
          }

          return basicTransform;
        });

        setUsers(transformedUsers);
      }
    } catch (err) {
      console.error('Error refreshing users:', err);
    } finally {
      setLoading(false);
    }
  };

  // Theo dõi thay đổi URL params để cập nhật filter
  useEffect(() => {
    const departmentFromUrl = searchParams.get('department');
    if (departmentFromUrl) {
      const decodedDepartment = decodeURIComponent(departmentFromUrl);
      setFilter(prevFilter => ({
        ...prevFilter,
        department: decodedDepartment
      }));
    }
  }, [searchParams]);

  // Load dữ liệu khi component mount
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch departments trước để có mapping
        let deptMapping = {};
        try {
          const deptResponse = await getAllDepartments();
          if (deptResponse.success) {
            (deptResponse.data || []).forEach(dept => {
              if (dept._id || dept.id) {
                deptMapping[dept._id || dept.id] = dept.name || 'Chưa có tên';
              }
            });
            setDepartmentMap(deptMapping);

          }
        } catch (deptError) {
          console.warn('Không thể tải danh sách phòng ban:', deptError.message);
          // Sử dụng mapping mặc định nếu không thể tải departments
          deptMapping = {
            'it': 'Phòng IT',
            'marketing': 'Phòng Marketing',
            'hr': 'Phòng Nhân sự',
            'accounting': 'Phòng Kế toán',
            'sales': 'Phòng Kinh doanh'
          };
          setDepartmentMap(deptMapping);
        }

        // Sau đó fetch users với department mapping
        const userResponse = await getAllUsers({
          populate: 'department,departmentId',
          include: 'department'
        });

        if (userResponse.success) {


          // Transform users với department mapping
          const transformedUsers = (userResponse.data || []).map(backendUser => {
            const basicTransform = transformUserData(backendUser);

            // Nếu department chưa có tên hoặc là ID, thử lookup từ mapping
            if (basicTransform.department === 'Chưa phân công' || basicTransform.department.startsWith('ID:')) {
              const departmentId = basicTransform.department.startsWith('ID:')
                ? basicTransform.department.replace('ID:', '')
                : (backendUser.departmentId || backendUser.department);

              if (departmentId && deptMapping[departmentId]) {
                basicTransform.department = deptMapping[departmentId];
              } else {
                // Fallback - thử tìm trong mapping bằng cách khác
                const foundDept = Object.entries(deptMapping).find(([id, name]) =>
                  id === departmentId || name.toLowerCase().includes(departmentId?.toLowerCase() || '')
                );
                if (foundDept) {
                  basicTransform.department = foundDept[1];
                }
              }
            }

            return basicTransform;
          });

          setUsers(transformedUsers);
        } else {
          setError(userResponse.message || 'Không thể tải danh sách nhân viên');
        }
      } catch (err) {
        setError(err.message || 'Có lỗi xảy ra khi tải dữ liệu');
        console.error('Error loading data:', err);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, []);

  // Thêm user mới vào danh sách (được gọi sau khi UserCreate đã tạo thành công)
  const handleAddUser = async (newUserData) => {
    try {
      // Chỉ refresh danh sách vì user đã được tạo thành công từ UserCreate component
      await refreshUsers();
    } catch (err) {
      setError(err.message || 'Có lỗi xảy ra khi cập nhật danh sách');
      console.error('Error refreshing user list:', err);
    }
  };

  // Cập nhật user
  const handleUpdateUser = async (idx, updatedUserData) => {
    try {
      const user = users[idx];
      if (!user || !user.id) {
        setError('Không tìm thấy thông tin nhân viên');
        return;
      }

      setLoading(true);

      // Nếu là thao tác khóa/mở khóa tài khoản
      if (updatedUserData.status === 'locked') {
        const response = await toggleUserStatus(user.id, true);
        if (response.success) {
          await refreshUsers(); // Refresh danh sách
        } else {
          setError(response.message || 'Không thể khóa tài khoản');
        }
      } else {
        // Cập nhật thông tin thông thường
        const response = await updateUser(user.id, updatedUserData);
        if (response.success) {
          await refreshUsers(); // Refresh danh sách
        } else {
          setError(response.message || 'Không thể cập nhật thông tin nhân viên');
        }
      }
    } catch (err) {
      setError(err.message || 'Có lỗi xảy ra khi cập nhật nhân viên');
      console.error('Error updating user:', err);
    } finally {
      setLoading(false);
    }
  };

  // Lọc users theo filter
  const filteredUsers = users.filter(u => {
    // Helper function để normalize text cho so sánh
    const normalizeText = (text) => {
      if (!text) return '';
      return text.toString().toLowerCase().trim();
    };

    // Helper function để kiểm tra text có chứa search term không
    const containsText = (text, searchTerm) => {
      if (!searchTerm) return true;
      return normalizeText(text).includes(normalizeText(searchTerm));
    };



    // Filter theo department - hỗ trợ cả name và ID
    let matchDepartment = filter.department === 'Tất cả phòng ban';
    
    if (!matchDepartment) {
      // Thử match theo name
      matchDepartment = normalizeText(u.department) === normalizeText(filter.department);
      
      // Nếu không match theo name, thử match theo department ID
      if (!matchDepartment && u.departmentId) {
        matchDepartment = u.departmentId === filter.department;
      }
      
      // Thử match với department info nếu có
      if (!matchDepartment && u.departmentInfo) {
        matchDepartment = u.departmentInfo.id === filter.department || 
                         normalizeText(u.departmentInfo.name) === normalizeText(filter.department);
      }
    }
    


    // Filter theo role
    const matchRole = filter.role === 'Tất cả chức vụ' || 
      normalizeText(u.role) === normalizeText(filter.role);

    // Filter theo status
    const matchStatus = filter.status === 'Tất cả trạng thái' ||
      (filter.status === 'Hoạt động' && u.status === 'active') ||
      (filter.status === 'Đã khoá' && u.status === 'locked');

    // Filter theo search term
    const matchSearch = !filter.search || 
      containsText(u.name, filter.search) ||
      containsText(u.email, filter.search) ||
      containsText(u.id, filter.search) ||
      containsText(u.department, filter.search) ||
      containsText(u.role, filter.search);

    return matchDepartment && matchRole && matchStatus && matchSearch;
  });

  // Tạo danh sách role động từ users
  const dynamicRoles = React.useMemo(() => {
    const allRoles = users.map(u => u.role).filter(Boolean);
    const uniqueRoles = Array.from(new Set(allRoles.map(r => r.trim())));
    return ['Tất cả chức vụ', ...uniqueRoles];
  }, [users]);

  // Tạo danh sách phòng ban động từ users
  const dynamicDepartments = React.useMemo(() => {
    const allDepartments = users.map(u => u.department).filter(Boolean);
    const uniqueDepartments = Array.from(new Set(allDepartments.map(d => d.trim())));
    return ['Tất cả phòng ban', ...uniqueDepartments];
  }, [users]);

  return (
    <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'stretch', overflow: 'auto' }}>
      {error && (
        <div style={{
          background: '#fee',
          border: '1px solid #fcc',
          color: '#c33',
          padding: '12px',
          borderRadius: '4px',
          marginBottom: '16px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between'
        }}>
          <span>{error}</span>
          <button
            onClick={() => setError(null)}
            style={{
              background: 'none',
              border: 'none',
              color: '#c33',
              cursor: 'pointer',
              fontSize: '18px',
              padding: '0 8px'
            }}
          >
            ×
          </button>
        </div>
      )}
      <HRLayout
        users={users}
        filteredUsers={filteredUsers}
        onAddUser={handleAddUser}
        onFilterChange={setFilter}
        roles={dynamicRoles}
        departments={dynamicDepartments}
      />
      <ListUser
        users={loading ? [] : filteredUsers}
        searchTerm={filter.search}
        onUpdateUser={handleUpdateUser}
        loading={loading}
        fetchUsersApi={getAllUsers}
      />
    </div>
  );
};

export default HRPage;

@import url('../index.css');

.kanban-board {
  display: flex;
  gap: 15px;
  /* padding: 24px; */
  background: #fff;
  height: 100vh;
  overflow-x: auto;
  overflow-y: hidden;
}
.kanban-column {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.06);
  min-width: 337px;
  width: 337px;
  flex-shrink: 0;
  padding: 16px;
  display: flex;
  flex-direction: column;

}
.kanban-column-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  margin-bottom: 16px;
}
.kanban-column-title {
  font-size: 13px;
  font-weight: 400;
  color: #5D5D5D;
}
.kanban-column-count {
  color: #888;
  font-size: 14px;
  font-weight: 400;
}
.kanban-tasks {
  display: flex;
  align-items: center;
  flex-direction: column;
  gap: 16px;
  flex: 1;
  padding-right: 4px;
  min-height: 0;
}
.kanban-tasks::-webkit-scrollbar {
  width: 8px;
  background: transparent;
  transition: background 0.2s;
}
.kanban-tasks::-webkit-scrollbar-thumb {
  background: transparent;
  border-radius: 4px;
  transition: background 0.2s;
}
.kanban-tasks:hover::-webkit-scrollbar {
  background: #eee;
}
.kanban-tasks:hover::-webkit-scrollbar-thumb {
  background: #bdbdbd;
}
.kanban-task {
  /* background: #f3f4f6; */
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 1px 4px rgba(0,0,0,0.1);
  display: flex;
  flex-direction: column;
  gap: 12px;
  transition: box-shadow 0.2s;
  width: 305px;
}
.kanban-task:hover {
  box-shadow: 0 4px 16px rgba(0,0,0,0.10);
}
.kanban-task-header {
  display: flex;
  align-items: center;
  gap: 16px;
}
.kanban-task-id {
  font-weight: 500;
  color: #7C7C7C;
  font-size: 14px;
}
.kanban-task-title {
  font-size: 14px;
  font-weight: 500;
  color: #7C7C7C;
}
.kanban-task-priority {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  color: #7C7C7C;
  font-weight: 500;
}
.kanban-task-priority.cao {
  color: #ef4444;
}
.kanban-task-priority.trung {
  color: #f59e42;
}

.kanban-task-dates-value {
  color: #5D5D5D;
  font-size: 14px;
}

.kanban-task-dates {
  display: flex;
  flex-direction: column;
  gap: 12px;
  min-width: 0;
}
.kanban-task-dates-row {
  display: flex;
  align-items: center;
}
.kanban-task-dates-label {
  min-width: 106px;
  color: #7C7C7C;
  font-size: 14px;
  font-weight: 500;
}
.kanban-task-dates-icon {
  width: 20px;
  height: 20px;
  margin: 0 8px;
  flex-shrink: 0;
}
.kanban-task-members {
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 4px;
  color: #8a8a8a;
}
.kanban-task-comments {
  font-size: 13px;
  color: #888;
  display: flex;
  align-items: center;
  gap: 4px;
}
.kanban-task-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

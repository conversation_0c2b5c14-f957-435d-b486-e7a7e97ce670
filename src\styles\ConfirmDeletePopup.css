.confirm-delete-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.confirm-delete-popup {
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  width: 400px;
  max-width: 90vw;
  animation: confirmDeleteFadeIn 0.2s ease-out;
}

@keyframes confirmDeleteFadeIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.confirm-delete-header {
  padding: 20px 20px 0 20px;
  border-bottom: 1px solid #eee;
  margin-bottom: 0;
}

.confirm-delete-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
  padding-bottom: 15px;
}

.confirm-delete-content {
  padding: 20px;
}

.confirm-delete-content p {
  margin: 0 0 12px 0;
  color: #555;
  line-height: 1.5;
}

.confirm-delete-warning {
  color: #dc3545 !important;
  font-size: 14px;
  font-style: italic;
}

.confirm-delete-actions {
  padding: 0 20px 20px 20px;
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.confirm-delete-cancel,
.confirm-delete-confirm {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.confirm-delete-cancel {
  background-color: #f8f9fa;
  color: #6c757d;
  border: 1px solid #dee2e6;
}

.confirm-delete-cancel:hover {
  background-color: #e9ecef;
  color: #495057;
}

.confirm-delete-confirm {
  background-color: #dc3545;
  color: white;
}

.confirm-delete-confirm:hover {
  background-color: #c82333;
}

.confirm-delete-confirm:active {
  transform: translateY(1px);
}

.confirm-delete-cancel:active {
  transform: translateY(1px);
}

import { useState, useEffect } from 'react';
import { NOTIFICATION_ENDPOINTS } from '../../../api/endpoints';

const getToken = () => {
  return localStorage.getItem('token') || '';
};

const CmtNoti = ({ globalFilter = 'all' }) => {
  const [notifications, setNotifications] = useState([]);
  const [loading, setLoading] = useState(false);

  // Fetch notifications from API
  const fetchNotifications = async () => {
    setLoading(true);
    try {
      const token = getToken();
      const res = await fetch(NOTIFICATION_ENDPOINTS.ALL, {
        headers: { Authorization: `Bearer ${token}` },
      });
      const dataRaw = await res.json();
      let data = dataRaw?.data || dataRaw || [];
      // Filter only comment notifications
      data = data.filter(noti => [
        'new_comment',
        'reply',
        'mention',
        'like',
        'urgent',
        'resolved'
      ].includes(noti.type));
      // Apply globalFilter
      if (globalFilter === 'unread') {
        data = data.filter(noti => !noti.isRead);
      } else if (globalFilter === 'read') {
        data = data.filter(noti => noti.isRead);
      }
      setNotifications(data);
    } catch (err) {
      setNotifications([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchNotifications();
    // eslint-disable-next-line
  }, [globalFilter]);

  const handleMarkAsRead = async (notificationId) => {
    try {
      const token = getToken();
      await fetch(NOTIFICATION_ENDPOINTS.MARK_READ(notificationId), {
        method: 'PUT',
        headers: { Authorization: `Bearer ${token}` },
      });
      fetchNotifications();
    } catch (err) {
      // handle error if needed
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'high': return '#ff4757';
      case 'medium': return '#ffa502';
      case 'low': return '#2ed573';
      default: return '#747d8c';
    }
  };

  const getTypeIcon = (type) => {
    switch (type) {
      case 'new_comment': return '💬';
      case 'reply': return '↩️';
      case 'mention': return '@';
      case 'like': return '👍';
      case 'urgent': return '🚨';
      case 'resolved': return '✅';
      default: return '💭';
    }
  };

  return (
    <div className="comment-notifications">
      {/* Notifications list */}
      <div className="notifications-list">
        {loading ? (
          <div className="no-notifications"><p>Đang tải thông báo...</p></div>
        ) : notifications.length === 0 ? (
          <div className="no-notifications">
            <p>Không có thông báo bình luận nào</p>
          </div>
        ) : (
          notifications.map(notification => (
            <div
              key={notification.id}
              className={`notification-item-simple ${notification.isRead ? 'read' : 'unread'}`}
              onClick={() => handleMarkAsRead(notification.id)}
            >
              <div className="notification-left-border" style={{ background: getPriorityColor(notification.priority) }}></div>
              <div className="notification-simple-content">
                <div className="notification-sender">
                  {notification.author?.fullName || notification.author?.name || notification.author?.email || notification.sender || 'Hệ thống'}
                </div>
                <div className="notification-text">
                  {getTypeIcon(notification.type)} {notification.message || notification.content} • <span className="notification-simple-time">{notification.time || notification.createdAt}</span>
                </div>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default CmtNoti;
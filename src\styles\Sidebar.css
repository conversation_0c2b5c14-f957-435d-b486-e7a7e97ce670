@import url('../index.css');
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

.dashboard-sidebar {
  width: 250px;
  height: 100vh;
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
  border-right: 1px solid #e0e0e0;
  position: fixed;
  top: 0;
  left: 0;
  /* z-index: 1000; */
}

.dashboard-logo {
  padding: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid #f0f0f0;
}

.dashboard-logo img {
  height: 60px;
}

.dashboard-menu {
  flex: 1;
  overflow-y: auto;
  padding: 10px 0;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.dashboard-menu::-webkit-scrollbar {
  display: none;
}

.dashboard-menu ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.dashboard-menu li {
  margin: 2px 0;
}

.dashboard-menu li a,
.dashboard-menu .menu-item,
.dashboard-footer a {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  color: #4a4a4a;
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  /* Remove transition to avoid color delay */
}

.dashboard-menu .active a,
.dashboard-menu li.active a,
.active-link {
  background-color: #007BFF;
  color: white !important; /* Force white color with !important */
  border-radius: 6px;
  font-weight: 500;
}

/* Update icon colors on active */
.dashboard-menu .active a .sidebar-icon,
.dashboard-menu li.active a .sidebar-icon,
.active-link .sidebar-icon {
  filter: brightness(0) saturate(100%) invert(100%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(100%) contrast(100%) !important;
}

.sidebar-icon {
  width: 16px;
  height: 16px;
  margin-right: 12px;
}

.sidebar-hashtag {
  font-size: 16px;
  font-weight: bold;
  margin-right: 12px;
  color: rgba(0, 0, 0, 0.3);
}

.dropdown-icon {
  margin-left: auto;
  display: flex;
  align-items: center;
}

.dropdown-icon img {
  width: 16px;
  height: 16px;
  margin-right: 60px;
}

/* Add left border for submenu items */
.submenu {
  padding-left: 0 !important;
  position: relative;
  margin-left: 24px;
  border-left: 1px solid #e0e0e0;
}

.submenu li {
  position: relative;
  padding-left: 15px;
}

/* Remove the horizontal line */
.submenu li::before {
  content: none;
}

.submenu li a,
.submenu .menu-item {
  padding: 8px 16px;
  display: flex;
  align-items: center;
  text-decoration: none;
  color: #4a4a4a;
}

/* Adjust nested submenu items */
.submenu .submenu {
  margin-left: 15px;
}

.dashboard-footer {
  border-top: 1px solid #f0f0f0;
  padding: 10px 6px;
  display: flex;
  justify-content: center;
}

.dashboard-footer a {
  width: 100%;
  justify-content: center;
}

.dashboard-footer .active-link {
  width: 100%;
  justify-content: center;
}

/* Update active styling */
.submenu li.active a {
  background-color: #007BFF;
  color: white !important;
  border-radius: 6px;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .dashboard-sidebar {
    width: 60px;
  }
  
  .dashboard-menu li a span,
  .dashboard-menu .menu-item span:not(.sidebar-hashtag):not(.dropdown-icon),
  .dashboard-footer a span {
    display: none;
  }
  
  .dropdown-icon img {
    margin-left: -10px; /* Adjust position when text is hidden */
  }
  
  .submenu {
    position: absolute;
    left: 60px;
    background: white;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
    z-index: 100;
    border-radius: 8px;
    min-width: 180px;
    margin-left: 0;
    border-left: none;
  }
}

/* Active state */
.dashboard-menu .active a,
.dashboard-menu li.active a,
.submenu li.active a,
.active-link {
  background-color: #007BFF;
  color: white !important;
  border-radius: 6px;
  font-weight: 500;
}

/* Icon colors for active state */
.submenu li.active a .sidebar-icon,
.active-link .sidebar-icon {
  filter: brightness(0) saturate(100%) invert(100%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(100%) contrast(100%) !important;
}

/* Active state styling */
.dashboard-menu li.active:not(.hovered) a,
.submenu li.active:not(.hovered) a,
.active-link:not(.hovered) {
  background-color: #007BFF;
  color: white !important;
  font-weight: 600;
}

/* Active dropdown icon color */
.active-link .dropdown-icon img,
.dashboard-menu li.active .dropdown-icon img,
.submenu li.active .dropdown-icon img {
  filter: brightness(0) saturate(100%) invert(100%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(100%) contrast(100%) !important;
}

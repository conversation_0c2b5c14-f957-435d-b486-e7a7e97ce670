@import url('../index.css');
.documents-main-content {
  max-width: 100%;
  min-width: 1000px;
  flex: 1;
  display: flex;
  flex-direction: column;
  height: calc(100vh - 139px); /* Fixed height to prevent page scroll */
}

.documents-container {
  background-color: #f8f9fa;
  height: 100%; /* Use full height of parent */
  display: flex;
  flex-direction: column;
  overflow: hidden; /* Ngăn scroll của container */
}

.documents-header {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 24px;
  background-color: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.documents-title-row {
  display: flex;
  align-items: center;
}

.documents-controls-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
}

.documents-title {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-shrink: 0;
}

.documents-search {
  flex: 1;
  display: flex;
  justify-content: flex-start;
  max-width: 1200px;
  margin-right: 20px;
}

.document-header-icon {
  width: 20px;
  height: 20px;
}

.documents-header h1 {
  font-size: 22px;
  font-weight: 600;
  color: #5D5D5D;
  margin: 0;
}

.documents-actions {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-shrink: 0;
}

.doc-search-container {
  position: relative;
  width: 100%;
  max-width: 1100px;
}

.search-icon {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  opacity: 0.6;
  z-index: 2;
  pointer-events: none;
}

.doc-search-input {
padding: 8px 35px 8px 15px;
    border: 1px solid #e0e0e0;
    border-radius: 16px !important;
    width: 100%;
    box-sizing: border-box;
    outline: none;
    position: relative;
    z-index: 1;
    transition: border-color 0.2s ease;
  }

.doc-search-input:focus {
  border-color: #007bff;
}

/* Sort container for positioning */
.sort-container {
  position: relative;
  display: inline-block;
}

.btn-sort {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background-color: #ffffff;
  color: #5D5D5D;
  border: 1px solid #ffffff;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-sort:hover {
background-color: #f5f5f5;
}

.add-document-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  white-space: nowrap;
}

.add-document-btn:hover {
  background-color: #0069d9;
}

.add-icon {
  width: 16px;
  height: 16px;
}

.documents-table-container {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  flex: 1; /* Chiếm hết không gian còn lại */
  overflow: auto; /* Cho phép scroll trong bảng */
  /* Custom scrollbar */
  scrollbar-width: thin;
  scrollbar-color: transparent transparent;
  transition: scrollbar-color 0.3s ease;
}

/* Custom scrollbar cho Webkit browsers (Chrome, Safari, Edge) */
.documents-table-container::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.documents-table-container::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 3px;
}

.documents-table-container::-webkit-scrollbar-thumb {
  background: transparent;
  border-radius: 3px;
  transition: background 0.3s ease;
}

/* Hiện scrollbar khi hover */
.documents-table-container:hover {
  scrollbar-color: rgba(0, 0, 0, 0.3) transparent;
}

.documents-table-container:hover::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.3);
}

.documents-table-container:hover::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.5);
}

.documents-table {
  width: 100%;
  border-collapse: collapse;
  table-layout: fixed; /* Cố định layout bảng */
}

.documents-table th,
.documents-table td {
  padding: 16px;
  text-align: left;
  border-bottom: 1px solid #f3f4f6;
  font-size: 14px;
  color: #7C7C7C;
  height: 30px; /* Cố định chiều cao mỗi hàng */
  vertical-align: middle; /* Căn giữa theo chiều dọc */
}

/* Căn giữa các cột từ cột 2 trở đi (Ngày tạo, Người tạo, Tải file, Xóa) */
.documents-table td:nth-child(2),
.documents-table th:nth-child(2),
.documents-table td:nth-child(3),
.documents-table th:nth-child(3),
.documents-table td:nth-child(4),
.documents-table th:nth-child(4),
.documents-table td:nth-child(5),
.documents-table th:nth-child(5) {
  text-align: center;
}

/* Điều chỉnh độ rộng các cột để có khoảng cách đồng đều */
.documents-table th:first-child,
.documents-table td:first-child {
  width: 40%;
  text-align: left;
}

.documents-table th:nth-child(2),
.documents-table td:nth-child(2) {
  width: 15%;
}

.documents-table th:nth-child(3),
.documents-table td:nth-child(3) {
  width: 15%;
}

.documents-table th:nth-child(4),
.documents-table td:nth-child(4) {
  width: 15%;
}

.documents-table th:nth-child(5),
.documents-table td:nth-child(5) {
  width: 15%;
}

.documents-table th {
  background-color: #f9fafb;
  font-weight: 600;
  font-size: 14px;
  color: #7C7C7C;
  user-select: none;
  letter-spacing: 0.05em;
  position: sticky; /* Làm header cố định */
  top: 0;
  z-index: 10;
  height: 50px; /* Chiều cao cố định cho header */
}

.documents-table tbody {
  vertical-align: top; /* Đảm bảo nội dung tbody không bị stretch */
}

.documents-table tbody tr {
  height: 60px; /* Cố định chiều cao mỗi hàng dữ liệu */
}

.document-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.file-text-icon {
  width: 16px;
  height: 16px; 
  opacity: 0.6;
}

.creator-cell {
  padding: 16px;
  text-align: center;
}

.creator-info {
  display: flex;
  align-items: center;
  justify-content: center;
}

.creator-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
}

.download-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 5px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.download-btn:hover {
  background-color: #f8f9fa;
}

.download-icon {
  width: 18px;
  height: 18px;
}

.deletes-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 5px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.deletes-btn:hover {
  background-color: #ffebee;
}

.delete-icon {
  width: 18px;
  height: 18px;
  color: #d32f2f;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  font-size: 16px;
  color: #6c757d;
}

.no-documents {
  text-align: center;
  height: 60px; /* Cố định chiều cao cho hàng empty */
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  height: 200px; /* Cố định chiều cao cho empty state */
}

.empty-icon {
  width: 48px;
  height: 48px;
  opacity: 0.5;
  margin-bottom: 16px;
}

.empty-state p {
  font-size: 16px;
  color: #6c757d;
  margin: 0;
}

/* Scrollbar cho toàn bộ trang (nếu cần) */
body::-webkit-scrollbar {
  width: 6px;
}

body::-webkit-scrollbar-track {
  background: transparent;
}

body::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

body::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.4);
}

/* Responsive Design */
@media (max-width: 1400px) {
  .documents-main-content {
    margin: 10px 0px 0 200px;
    min-width: 800px;
  }
}

@media (max-width: 1200px) {
  .documents-main-content {
    margin: 10px 0px 0 180px;
    min-width: 700px;
  }

  .documents-container {
    height: calc(100vh - 120px);
  }

  .search-container {
    width: 250px;
  }
}

@media (max-width: 992px) {
  .documents-main-content {
    margin: 10px 20px 0 160px;
    min-width: 600px;
  }

  .documents-controls-row {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .documents-search {
    max-width: none;
  }

  .documents-actions {
    justify-content: flex-end;
  }

  .search-container {
    width: 100%;
    max-width: 400px;
  }
}

@media (max-width: 768px) {
  .documents-main-content {
    margin: 10px 15px 0 15px;
    min-width: auto;
  }

  .documents-container {
    height: calc(100vh - 100px);
    padding: 15px;
  }

  .documents-header h1 {
    font-size: 20px;
  }

  .documents-table th,
  .documents-table td {
    padding: 12px 8px;
    font-size: 13px;
  }

  .creator-avatar {
    width: 28px;
    height: 28px;
  }

  .download-icon {
    width: 16px;
    height: 16px;
  }

  .delete-icon {
    width: 16px;
    height: 16px;
  }

  /* Ẩn cột ngày tạo trên mobile */
  .documents-table th:nth-child(2),
  .documents-table td:nth-child(2) {
    display: none;
  }

  /* Điều chỉnh lại độ rộng cột cho mobile */
  .documents-table th:first-child,
  .documents-table td:first-child {
    width: 50%;
  }

  .documents-table th:nth-child(3),
  .documents-table td:nth-child(3) {
    width: 20%;
  }

  .documents-table th:nth-child(4),
  .documents-table td:nth-child(4) {
    width: 15%;
  }

  .documents-table th:nth-child(5),
  .documents-table td:nth-child(5) {
    width: 15%;
  }
}

@media (max-width: 576px) {
  .documents-main-content {
    margin: 5px 10px 0 10px;
  }

  .documents-container {
    padding: 10px;
  }

  .documents-header {
    margin-bottom: 16px;
  }

  .documents-header h1 {
    font-size: 18px;
  }

  .search-input {
    font-size: 14px;
    padding: 8px 8px 8px 35px;
  }

  .documents-table th,
  .documents-table td {
    padding: 8px 4px;
    font-size: 12px;
  }

  .document-info {
    gap: 6px;
  }

  .document-info span {
    font-size: 12px;
    line-height: 1.3;
  }

  .file-text-icon {
    width: 14px;
    height: 14px;
  }

  .creator-avatar {
    width: 24px;
    height: 24px;
  }

  .download-btn {
    padding: 3px;
  }

  .download-icon {
    width: 14px;
    height: 14px;
  }

  .deletes-btn {
    padding: 3px;
  }

  .delete-icon {
    width: 14px;
    height: 14px;
  }
}

@media (max-width: 480px) {
  .documents-main-content {
    margin: 5px 8px 0 8px;
  }

  .documents-container {
    padding: 8px;
  }

  .documents-header h1 {
    font-size: 16px;
  }

  .search-input {
    font-size: 13px;
    padding: 6px 6px 6px 30px;
  }

  .documents-table th,
  .documents-table td {
    padding: 6px 2px;
    font-size: 11px;
  }

  .document-info span {
    font-size: 11px;
  }

  /* Ẩn thêm cột creator trên màn hình rất nhỏ */
  .documents-table th:nth-child(3),
  .documents-table td:nth-child(3) {
    display: none;
  }

  /* Điều chỉnh độ rộng cho màn hình rất nhỏ - chỉ hiển thị tên, tải file và xóa */
  .documents-table th:first-child,
  .documents-table td:first-child {
    width: 60%;
  }

  .documents-table th:nth-child(4),
  .documents-table td:nth-child(4) {
    width: 20%;
  }

  .documents-table th:nth-child(5),
  .documents-table td:nth-child(5) {
    width: 20%;
  }
}

/* Creator tooltip styles */
.creator-tooltip {
  position: fixed;
  background-color: #333;
  color: white;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 1000;
  transform: translateX(-50%);
  white-space: nowrap;
  animation: fadeIn 0.15s ease-in-out;
}

.creator-tooltip::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: #333 transparent transparent transparent;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateX(-50%) translateY(-5px); }
  to { opacity: 1; transform: translateX(-50%) translateY(0); }
}

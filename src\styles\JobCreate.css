@import url('../index.css');
/* Backdrop cho panel JobCreate */
.job-create-panel-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.08);
  z-index: 2099;
}
/* --- Panel Layout for JobCreate (giống JobDetail) --- */
.job-create-panel-container {
  position: fixed;
  top: 0;
  right: 0;
  width: 420px;
  max-width: 100vw;
  height: 100vh;
  background: #f6f6f6;
  box-shadow: -2px 0 16px 0 rgba(60,72,88,0.10);
  padding: 0;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  animation: slideInRight 0.25s ease;
  min-height: 0;
}

@keyframes slideInRight {
  from { right: -500px; opacity: 0; }
  to { right: 0; opacity: 1; }
}

.job-create-panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px 10px 24px;
  border-bottom: 1px solid #eaeaea;
}
.job-create-panel-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}
.job-panel-close-btn {
  background: none;
  border: none;
  cursor: pointer;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.job-panel-close-btn img {
  width: 20px;
  height: 20px;
}

.job-create-panel-form {
  padding: 18px 24px 18px 24px;
  display: flex;
  flex-direction: column;
  gap: 0;
  height: 100%;
  overflow-y: auto;
  flex: 1 1 auto;
  min-height: 0;
}
.job-panel-section {
  display: flex;
  flex-direction: column;
  gap: 10px;
}
.job-panel-row {
  display: flex;
  align-items: flex-start;
  gap: 18px;
  margin-bottom: 0;
}
.job-panel-rows {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  margin-bottom: 0;
  flex-direction: column;
}
.job-panel-label {
  min-width: 120px;
  font-size: 14px;
  font-weight: 500;
  color: #5B5B5B;
}
.job-panel-value {
  flex: 1 1 0%;
  display: flex;
  align-items: center;
  gap: 10px;
  width: 100%;
  box-sizing: border-box;
}
.job-panel-avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  object-fit: cover;
}
.job-panel-member {
  display: inline-block;
  position: relative;
  margin-right: 8px;
  vertical-align: middle;
}
.job-panel-remove-member-btn {
  position: absolute;
  top: -3px;
  right: -2px;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  border: none;
  background: #007BFF;
  color: #ffffff;
  font-size: 10px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 1px 4px rgba(0,0,0,0.08);
  z-index: 2;
  transition: background 0.2s, color 0.2s;
  padding: 0;
}
.job-panel-remove-member-btn:hover {
  background: #032ff5;
  color: #fff;
}

.job-panel-add-member-btn {
  border: none;
  font-size: 13px;
  background-color: #f6f6f6;
  padding: 0;
  cursor: pointer;
  transition: background 0.2s;
}
.job-panel-add-member-btn img {
  width: 16px;
  height: 16px;
  filter: invert(50%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(90%) contrast(100%);
}
.job-panel-add-member-btn:hover img {
  filter: invert(36%) sepia(92%) saturate(3800%) hue-rotate(190deg) brightness(97%) contrast(101%);
}
.job-panel-icon {
  width: 18px;
  height: 18px;
  margin-right: 4px;
}
.job-panel-file-upload {
  display: flex;
  align-items: center;
  gap: 8px;
  background: #f8f9ff;
  border-radius: 8px;
  border: 1px dashed #e0e0e0;
  padding: 8px 12px;
  min-width: 180px;
}
.job-panel-file-upload-text {
  font-size: 12px;
  color: #999;
}
.job-panel-file-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-top: 6px;
  max-height: 100px;
  overflow-y: auto;
}
.job-panel-file-item {
  display: flex;
  align-items: center;
  gap: 6px;
  background: #fff;
  border-radius: 6px;
  border: 1px solid #e0e0e0;
  padding: 4px 8px;
}
.job-panel-file-icon {
  width: 16px;
  height: 16px;
}
.job-panel-file-name {
  font-size: 12px;
  font-weight: 500;
  color: #5d5d5d;
  flex: 1;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.job-panel-remove-file-btn {
  background: #007BFF;
  color: #ffffff;
  border: none;
  border-radius: 50%;
  width: 12px;
  height: 12px;
  font-size: 12px;
  cursor: pointer;
  margin-left: 2px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.2s;
}
.job-panel-remove-file-btn:hover {
  background: #032ff5;
  color: #fff;
}
.job-panel-action {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 24px;
}
.job-panel-cancel-btn {
  background: #f6f6f6;
  color: #5d5d5d;
  border: none;
  border-radius: 6px;
  padding: 8px 18px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s;
}
.job-panel-cancel-btn:hover {
  background: #e0e0e0;
}
.job-panel-create-btn {
  background: #007bff;
  color: #fff;
  border: none;
  border-radius: 6px;
  padding: 8px 22px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0,123,255,0.07);
  transition: background 0.2s, box-shadow 0.2s;
}
.job-panel-create-btn:hover {
  background: #0056b3;
  box-shadow: 0 4px 16px rgba(0,123,255,0.13);
}
.error-message-panel {
  color: #dc3545;
  background-color: #f6f6f6;
  border:none;
  border-radius: 4px;
  padding: 8px 8px 8% 0;
  margin-bottom: 4px;
  font-size: 12px;
  font-weight: 500;
  word-break: break-word;
}

@media (max-width: 900px) {
  .job-create-panel-container {
    width: 100vw;
    border-radius: 0;
    min-width: 0;
  }
  .job-create-panel-header, .job-create-panel-form {
    padding-left: 10px;
    padding-right: 10px;
  }
}

.job-create-modal-bg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.08);
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.job-create-container {
  background: #fff;
  border-radius: 16px;
  padding: 20px;
  width: 500px;
  max-width: 90vw;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  position: relative;
  max-height: 90vh;
  overflow-y: auto;
}

.job-create-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.job-create-header h2 {
  font-size: 20px;
  font-weight: 500;
  color: #5B5B5B;
  margin: 0;
}

.job-close-btn {
  background: none;
  border: none;
  font-size: 24px;
  color: #999;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.job-close-btn img {
  width: 20px;
  height: 20px;
}

.job-close-btn:hover {
  color: #666;
}

.job-create-form {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.job-form-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
  font-size: 14px;
}

.job-form-group label {
  font-size: 12px;
  font-weight: 500;
  color: #5B5B5B;
  margin-bottom: 1px;
  display: block;
}

/* Làm cho dấu * trong label có màu đỏ */
label > span.required-asterisk {
  color: #dc3545;
}

.job-form-group input,
.job-form-group textarea {
  padding: 6px 8px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  font-size: 12px;
  font-family: 'Be Vietnam Pro', Arial, sans-serif;
  line-height: 1.3;
  color: #000;
  background: #fff;
  transition: border-color 0.2s ease;
}

.job-form-group input:focus,
.job-form-group textarea:focus {
  outline: none;
  border-color: #007bff;
}

.job-form-group input::placeholder,
.job-form-group textarea::placeholder {
  color: #999;
}

.job-form-group textarea {
  resize: vertical;
  min-height: 32px;
  max-height: 70px;
}

.job-form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
  margin-bottom: 0;
}

.job-members-files-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
  align-items: start;
  margin-top: 5px;
  min-height: 120px;
}

.job-members-files-row .job-form-group {
  min-width: 0;
}

.job-members-section {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 6px;
  margin-top: 0;
}

.job-member-avatars {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
  min-height: 30px;
}

.job-members-avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  overflow: visible;
  border: 1px solid #e0e0e0;
  position: relative;
  cursor: pointer;
  margin-right: 6px;
}

.job-members-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

.job-remove-member-btn {
  position: absolute;
  top: -5px;
  right: -5px;
  width: 14px;
  height: 14px;
  border-radius: 50%;
  border: none;
  background: #ff4757;
  color: white;
  font-size: 9px;
  font-weight: bold;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  z-index: 10;
}

.job-members-avatar:hover .job-remove-member-btn {
  transform: scale(1.1);
}

.job-remove-member-btn:hover {
  background: #ff3742;
  transform: scale(1.1);
}

.job-add-member-btn {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  border: 1px solid #fff;
  background: #fff;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  padding: 0;
  outline: none;
}

.job-add-member-btn img {
  width: 16px;
  height: 16px;
  filter: invert(50%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(90%) contrast(100%);
}

.job-add-member-btn:hover img {
  filter: invert(36%) sepia(92%) saturate(3800%) hue-rotate(190deg) brightness(97%) contrast(101%);
}

.job-file-items {
  display: flex;
  flex-direction: column;
  gap: 6px;
  margin-top: 0;
}

.job-add-file-btn {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  border: 1px solid #fff;
  background: #fff;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  padding: 0;
  outline: none;
}

.job-add-file-btn img {
  width: 16px;
  height: 16px;
  filter: invert(50%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(90%) contrast(100%);
}

.job-add-file-btn:hover img {
  filter: invert(36%) sepia(92%) saturate(3800%) hue-rotate(190deg) brightness(97%) contrast(101%);
}

.job-file-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
  max-height: 65px;
  overflow-y: auto;
  padding-right: 4px;
  width: calc(100% - 26px);
  box-sizing: border-box;
}

.job-file-list::-webkit-scrollbar {
  width: 4px;
}

.job-file-list::-webkit-scrollbar-track {
  background: transparent;
}

.job-file-list::-webkit-scrollbar-thumb {
  background: #ddd;
  border-radius: 2px;
}

.job-file-list::-webkit-scrollbar-thumb:hover {
  background: #bbb;
}

.job-file-item {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px;
  background: #f8f9ff;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
  position: relative;
  width: 100%;
  box-sizing: border-box;
}

.job-file-icon {
  width: 14px;
  height: 14px;
  filter: brightness(0) saturate(100%);
  flex-shrink: 0;
}

.job-file-name {
  font-size: 12px;
  color: #333;
  flex: 1;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: calc(100% - 40px);
}

.job-remove-file-btn {
  width: 16px;
  height: 16px;
  border: none;
  background: #ff4757;
  color: white;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  transition: all 0.2s ease;
  flex-shrink: 0;
  margin-left: auto;
}

.job-remove-file-btn:hover {
  background: #ff3742;
  transform: scale(1.1);
}

.job-remove-file-btn:active {
  transform: scale(0.95);
}

.job-create-panel-footer {
  position: sticky;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  background: #f6f6f6;
  border-top: 1px solid #eaeaea;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding: 16px 24px;
  z-index: 10;
  box-sizing: border-box;
}

.job-form-action {
  display: flex;
  justify-content: center;
  padding-top: 10px;
  width: 100%;
}

.job-create-btn {
  background: #007bff;
  color: #fff;
  border: none;
  border-radius: 4px;
  padding: 8px 20px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s, box-shadow 0.2s;
  width: 100%;
  box-shadow: 0 2px 8px rgba(0,123,255,0.07);
}

.job-create-btn:hover {
  background: #0056b3;
  box-shadow: 0 4px 16px rgba(0,123,255,0.13);
}

.job-create-btn:active {
  transform: translateY(1px);
}

/* Responsive design */
@media (max-width: 768px) {
  .job-create-container {
    width: 100%;
    margin: 0 4px;
    padding: 10px;
  }
  
  .job-form-row {
    grid-template-columns: 1fr;
    gap: 10px;
  }
  
  .job-members-files-row {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .job-member-avatars {
    flex-wrap: wrap;
  }
  .job-create-btn {
    min-width: 100%;
    font-size: 15px;
    padding: 10px 0;
  }
}

/* Date input styling - JobCreate specific */
.job-date-input-group {
  display: flex;
  align-items: center;
  background: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  padding: 0 8px;
  height: 32px;
  min-width: 180px;
  color: #666;
  font-size: 13px;
  position: relative;
  gap: 6px;
  transition: border-color 0.2s ease;
}

.job-date-input-group:hover {
  border-color: #bdbdbd;
}

.job-date-input-group:focus-within {
  border-color: #007bff;
}

.job-date-input-group input {
  border: none;
  outline: none;
  background: transparent;
  color: #333;
  font-size: 12px;
  width: 100%;
  cursor: pointer;
}

/* Ẩn icon datepicker mặc định trên Chrome, Edge, Safari */
input[type="date"]::-webkit-calendar-picker-indicator {
  opacity: 0;
}
/* Ẩn icon datepicker trên Firefox */
input[type="date"]::-moz-calendar-picker-indicator {
  opacity: 0;
}
/* Ẩn icon datepicker trên Edge (mới) */
input[type="date"]::-ms-input-placeholder {
  opacity: 0;
}
/* 
input[type="date"] {
  border: none;
  border-radius: 4px;
  padding: 6px 8px;
  font-size: 13px;
  font-family: "'Be Vietnam Pro', Arial, sans-serif";
  background: #fff;
  width: 160px;
  outline: none;
}
input[type="date"]:focus {
  border-color: #007BFF;
} */

.job-date-input-group .job-calendar-icon {
  width: 16px;
  height: 16px;
  opacity: 1;
  color: #5B5B5B;
}

.job-date-input-wrapper {
  position: relative;
  display: inline-block;
  width: 170px;
}
.job-date-input-wrapper input[type="date"] {
  width: 100%;
  padding-left: 36px;
}
.job-date-input-icon {
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  width: 18px;
  height: 18px;
  pointer-events: none;  opacity: 0.8;
}

/* Field validation styles */
.job-form-group input.error,
.job-form-group textarea.error,
.job-date-input-group.error,
.job-form-group select.error {
  border-color: #dc3545 !important;
  box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.12);
}

.field-error {
  color: #dc3545;
  font-size: 11px;
  margin-top: 1px;
  display: block;
  min-height: 12px;
  line-height: 12px;
}

.error-message {
  color: #dc3545;
  background-color: #f8d7da;
  border: 1.5px solid #dc3545;
  border-radius: 4px;
  padding: 10px 14px;
  margin-bottom: 16px;
  font-size: 15px;
  font-weight: 500;
  word-break: break-word;
  box-shadow: 0 2px 8px rgba(220,53,69,0.07);
}

/* Custom select dropdown styling */
.job-form-group select {
  padding: 6px 8px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  font-size: 12px;
  font-family: 'Be Vietnam Pro', Arial, sans-serif;
  background: #fff;
  color: #000;
  transition: border-color 0.2s ease;
  height: 32px;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23999' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 8px center;
  background-size: 16px;
  padding-right: 28px;
  cursor: pointer;
}

.job-form-group select:focus {
  outline: none;
  border-color: #007bff;
}

/* Styling for dropdown options */
select option {
  padding: 8px 12px;
  font-size: 12px;
  color: #5B5B5B;
  background-color: #fff;
}

/* Webkit browsers custom styling */
select::-webkit-scrollbar {
  width: 4px;
}

select::-webkit-scrollbar-track {
  background: #f1f1f1;
}

select::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 10px;
}

select::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Firefox custom styling */
select {
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 #f1f1f1;
}

/* Custom dropdown styling */
.custom-select {
  position: relative;
  display: inline-block;
  width: 100%;
}

.job-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 1000;
  background: white;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-top: 4px;
  max-height: 220px;
  overflow-y: auto;
  border: 1px solid #e0e0e0;
}

.job-dropdown-item {
  padding: 8px 12px;
  cursor: pointer;
  font-size: 13px;
  font-weight: 500;
  color: #5B5B5B;
  transition: background-color 0.2s;
}

.job-dropdown-item:hover {
  background-color: #f5f5f5;
}

.job-dropdown-item:first-child {
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}

.job-dropdown-item:last-child {
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
}

.job-dropdown::-webkit-scrollbar {
  width: 4px;
}

.job-dropdown::-webkit-scrollbar-track {
  background: transparent;
}

.job-dropdown::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 10px;
}

.job-dropdown::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Firefox */
.job-dropdown {
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 transparent;
}

.job-panel-file-upload-custom {
  width: 100% !important;
  max-width: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 1px dashed #d3d3d3;
  border-radius: 12px;
  background: #fafafa;
  min-height: 140px;
  cursor: pointer;
  margin-top: 8px;
  margin-bottom: 16px;
  padding: 16px 0;
}
.job-panel-file-upload-custom:hover {
  border-color: #007BFF;
}
.job-panel-file-upload-icon {
  width: 56px;
  height: 56px;
  margin-bottom: 12px;
}
.job-panel-file-upload-text {
  color: #888;
  font-size: 14px;
  text-align: center;
}

/* Custom Date Input Group for JobCreate - Enhanced for better calendar picker */
.job-create-date-input-group-custom {
  display: flex;
  align-items: center;
  background: #fff;
  border: 1.5px solid #f6f6f6;
  border-radius: 8px;
  padding: 0 12px;
  height: 40px;
  min-width: 200px;
  color: #666;
  font-size: 14px;
  position: relative;
  gap: 8px;
  transition: all 0.2s ease;
  cursor: pointer;
  user-select: none;
}

.job-create-date-input-group-custom:hover {
  border-color: #007bff;
  background: #f8f9ff;
}

.job-create-date-input-group-custom:focus-within {
  border-color: #007bff;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.job-create-date-input-group-custom.error {
  border-color: #dc3545;
}

.job-create-date-input-group-custom.error:hover {
  border-color: #dc3545;
  background: #fff5f5;
}

.job-create-date-custom {
  border: none !important;
  outline: none !important;
  background: transparent !important;
  color: #333 !important;
  font-size: 14px !important;
  width: 100% !important;
  cursor: pointer !important;
  font-family: inherit !important;
  padding: 0 !important;
  margin: 0 !important;
}

.job-create-date-custom::-webkit-calendar-picker-indicator {
  opacity: 0;
  cursor: pointer;
  position: absolute;
  right: 0;
  width: 100%;
  height: 100%;
  background: transparent;
}

.job-create-date-custom::placeholder {
  color: #bdbdbd;
}

.job-create-date-custom:focus {
  outline: none !important;
  box-shadow: none !important;
}

.job-calendar-icon-custom {
  width: 18px;
  height: 18px;
  opacity: 0.7;
  color: #666;
  cursor: pointer;
  transition: opacity 0.2s ease;
}

.job-create-date-input-group-custom:hover .job-calendar-icon-custom {
  opacity: 1;
  color: #007bff;
}

.job-panel-members-flex-row {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  flex-wrap: wrap;
  min-height: 36px;
}
.job-panel-members-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
}

/* Job Create Validation Styles */
.job-create-field-wrapper {
  display: flex;
  flex-direction: column;
  margin-bottom: 18px;
}

.job-create-field-wrapper .job-panel-row {
  margin-bottom: 0;
}

.job-create-field-error {
  color: #ff4d4f;
  font-size: 12px;
  margin-top: 4px;
  margin-left: 138px; /* Align with input field (120px label + 18px gap) */
  padding-left: 0;
  display: block;
  line-height: 1.4;
}

.job-create-input {
  width: 100%;
  padding: 6px 8px;
  border: 1px solid #bdbdbd;
  border-radius: 4px;
  font-size: 14px;
  font-family: 'Be Vietnam Pro', Arial, sans-serif;
  outline: none;
  background-color: #fff;
  transition: border-color 0.2s ease;
}

.job-create-input.error {
  border-color: #ff4d4f;
}

.job-create-input:focus {
  border-color: #007bff;
}

.job-create-textarea {
  width: 100%;
  min-height: 60px;
  border-radius: 4px;
  border: 1px solid #bdbdbd;
  outline: none;
  font-size: 14px;
  font-family: 'Be Vietnam Pro', Arial, sans-serif;
  line-height: 1.5;
  background-color: #fff;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100px;
  box-sizing: border-box;
  padding: 8px;
  resize: vertical;
  transition: border-color 0.2s ease;
}

.job-create-textarea.error {
  border-color: #ff4d4f;
}

.job-create-textarea:focus {
  border-color: #007bff;
}

/* .job-create-date-custom {
  border: none;
  outline: none;
  background: transparent;
  color: #5b5b5b;
  font-size: 14px;
  width: 100%;
  cursor: pointer;
  font-family: inherit;
} */

.job-create-date-input-group-custom {
  display: flex;
  align-items: center;
  background: #fff;
  border: 1.5px solid #f6f6f6;
  border-radius: 4px;
  padding: 0 12px;
  height: 30px;
  min-width: 180px;
  color: #666;
  font-size: 15px;
  position: relative;
  gap: 8px;
  transition: border-color 0.2s ease;
}

.job-create-date-input-group-custom.error {
  border-color: #ff4d4f;
}

.job-create-date-input-group-custom:hover {
  border-color: #bdbdbd;
}

.job-create-date-input-group-custom:focus-within {
  border-color: #007bff;
}

/* .job-create-date-input-group-custom input {
  border: none;
  outline: none;
  background: transparent;
  color: #333;
  font-size: 15px;
  width: 100%;
  cursor: pointer;
} */

.job-create-date-input-group-custom input[type="date"] {
  border: none;
  outline: none;
  background: transparent;
  color: #5b5b5b;
  font-size: 14px;
  width: 100%;
  cursor: pointer;
  font-family: inherit;
}

.job-create-date-input-group-custom input[type="date"]::-webkit-calendar-picker-indicator {
  opacity: 0;
  cursor: pointer;
  position: absolute;
  right: 0;
  width: 100%;
  height: 100%;
}

.job-create-date-input-group-custom input::placeholder {
  color: #bdbdbd;
}

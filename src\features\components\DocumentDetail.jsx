import React, { useState, useEffect, useRef } from 'react';
import '../../styles/DocumentDetail.css';
import closePanelIcon from '../../assets/closePanel.svg';
import userIcon from '../../assets/user1.png';
import startdateIcon from '../../assets/startdate.svg';
import pencilLineIcon from '../../assets/pencil-line.svg';
import { FaPaperPlane, FaPaperclip } from 'react-icons/fa';
import { getDocument } from '../../api/documents';

const DocumentDetail = ({ document, onClose }) => {
  const [visible, setVisible] = useState(false);
  const [description, setDescription] = useState('');
  const [isEditingDescription, setIsEditingDescription] = useState(false);
  const [activities, setActivities] = useState([]);
  const [newComment, setNewComment] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const contentRef = useRef(null);
  const fileInputRef = useRef(null);



  useEffect(() => {
    let ignore = false;
    async function fetchDetail() {
      if (!document) return;
      setLoading(true);
      setError('');
      try {
        // Thử gọi API để lấy chi tiết, nếu lỗi thì sử dụng dữ liệu từ props
        const docDetail = await getDocument(document._id || document.id);
        if (!ignore) {
          setDescription(docDetail.description || document.description || 'Tài liệu này sử dụng với mục đích hướng dẫn người dùng có thể thao tác và sử dụng tốt ứng dụng trong dự án');
          setActivities(docDetail.activities || document.activities || []);
        }
      } catch (err) {
        // Nếu API lỗi, sử dụng dữ liệu từ props document
        console.warn('Không thể tải chi tiết từ API, sử dụng dữ liệu local:', err);
        if (!ignore) {
          setDescription(document.description || 'Tài liệu này sử dụng với mục đích hướng dẫn người dùng có thể thao tác và sử dụng tốt ứng dụng trong dự án');
          setActivities(document.activities || []);
          setError(''); // Không hiển thị lỗi vì đã có fallback data
        }
      } finally {
        if (!ignore) setLoading(false);
      }
    }
    if (document) {
      setTimeout(() => setVisible(true), 10);
      fetchDetail();
      if (contentRef.current) {
        contentRef.current.scrollTop = 0;
      }
    } else {
      setVisible(false);
    }
    return () => { ignore = true; };
  }, [document]);

  if (!document) return null;

  const handleOverlayClick = (e) => {
    if (e.target.classList.contains('document-detail-overlay')) {
      handleClose();
    }
  };

  const handleClose = () => {
    setVisible(false);
    setTimeout(() => onClose && onClose(), 250);
  };

  const handleEditDescription = () => {
    setIsEditingDescription(true);
  };

  const handleSaveDescription = () => {
    setIsEditingDescription(false);
    // Here you would typically save to backend
  };

  const handleCancelEditDescription = () => {
    setIsEditingDescription(false);
    // Reset to original description if needed
  };

  const handleAddComment = () => {
    if (newComment.trim()) {
      const comment = {
        id: Date.now(),
        user: 'Bạn',
        avatar: userIcon,
        content: newComment,
        timestamp: new Date().toLocaleString('vi-VN')
      };
      setActivities(prev => [comment, ...prev]);
      setNewComment('');
    }
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleAddComment();
    }
  };

  // Xử lý khi nhấn nút đính kèm file
  const handleAttachFile = () => {
    fileInputRef.current.click();
  };

  // Xử lý khi chọn file
  const handleFileSelected = (e) => {
    const files = Array.from(e.target.files);
    if (files.length > 0) {
      // Tạo hoạt động mới cho việc đính kèm file
      const newActivity = {
        id: Date.now(),
        user: 'Bạn',
        avatar: userIcon,
        content: `Đã đính kèm ${files.length} tệp: ${files.map(f => f.name).join(', ')}`,
        timestamp: new Date().toLocaleString('vi-VN')
      };

      setActivities(prev => [newActivity, ...prev]);

      // Reset input file
      e.target.value = '';
    }
  };

  return (
    <div className="document-detail-overlay" onClick={handleOverlayClick} style={{position: 'fixed', inset: 0, background: 'rgba(0,0,0,0.08)', zIndex: 999}}>
      <div className={`document-detail-container${visible ? ' show' : ''}`}>
        <button className="document-detail-close-btn" onClick={handleClose} title="Đóng">
          <img src={closePanelIcon} alt="Đóng" style={{width: 20, height: 20}} />
        </button>

        {/* Header cố định */}
        <div className="document-detail-header">
          <h3>{document.name}</h3>
        </div>

        <div className="document-detail-content" ref={contentRef}>
          {loading && <div className="document-detail-loading">Đang tải chi tiết...</div>}
          {error && <div className="document-detail-error">{error}</div>}
          {!loading && !error && (
            <>
              <div className="document-detail-row">
                <span className="document-detail-label">Người tạo</span>
                <span className="document-detail-value">
                  <img src={userIcon} alt="User" className="document-user-avatar" />
                  {document.creator}
                </span>
              </div>

              <div className="document-detail-row">
                <span className="document-detail-label">Dự án</span>
                <span className="document-detail-value">
                  {document.project || 'SDTC'}
                </span>
              </div>

              <div className="document-detail-row">
                <span className="document-detail-label">Ngày tạo</span>
                <span className="document-detail-value">
                  <img src={startdateIcon} alt="Ngày tạo" style={{ width: 16, marginRight: 8, verticalAlign: 'middle' }} />
                  {document.uploadedAt
                    ? new Date(document.uploadedAt).toLocaleDateString('vi-VN')
                    : document.createdAt
                      ? (document.createdAt.includes && document.createdAt.includes('/')
                          ? document.createdAt
                          : new Date(document.createdAt).toLocaleDateString('vi-VN'))
                      : 'N/A'
                  }
                </span>
              </div>

              <div className="document-detail-row">
                <span className="document-detail-label">
                  Mô tả
                  <button onClick={handleEditDescription} className="document-edit-btn">
                    <img src={pencilLineIcon} alt="Edit" />
                  </button>
                </span>
                <span className="document-detail-value document-description-section">
                  {isEditingDescription ? (
                    <div className="document-description-edit">
                      <textarea
                        value={description}
                        onChange={(e) => setDescription(e.target.value)}
                        className="document-description-textarea"
                        rows="3"
                      />
                      <div className="document-description-actions">
                        <button onClick={handleSaveDescription} className="document-btn-save">
                          Lưu
                        </button>
                        <button onClick={handleCancelEditDescription} className="document-btn-cancel">
                          Hủy
                        </button>
                      </div>
                    </div>
                  ) : (
                    <div className="document-description-display">
                      <span>{description}</span>
                    </div>
                  )}
                </span>
              </div>



              <div className="document-detail-row document-detail-row-activity">
                <span className="document-detail-label">Bình luận</span>
                <span className="document-detail-value">
                  <div className="document-detail-activity-list">
                    {activities.length > 0 ? activities.map((act, idx) => (
                      <div key={idx} className="document-detail-activity-item">
                        <img src={act.avatar || userIcon} alt={act.user} className="document-detail-activity-avatar" />
                        <div className="document-detail-activity-content">
                          <span className="document-detail-activity-name">{act.user}</span>
                          <span className="document-detail-activity-text">{act.content}</span>
                          {act.timestamp && <span className="document-detail-activity-time">{act.timestamp}</span>}
                        </div>
                      </div>
                    )) : <span style={{ color: '#888', fontStyle: 'italic' }}>Chưa có bình luận</span>}
                  </div>
                </span>
              </div>
            </>
          )}
        </div>

        {/* Comment footer cố định */}
        <div className="document-comment-footer">
          <div className="document-detail-row document-detail-row-comment">
            <div className="document-detail-comment-box-wrapper">
              <div className="document-detailfile-comment-box">
                <input
                  className="document-detail-comment-input"
                  placeholder="Viết bình luận cho bạn"
                  value={newComment}
                  onChange={(e) => setNewComment(e.target.value)}
                  onKeyDown={handleKeyDown}
                />
                <div className="document-detail-comment-actions">
                  <input
                    type="file"
                    ref={fileInputRef}
                    style={{ display: 'none' }}
                    multiple
                    onChange={handleFileSelected}
                  />
                  <button
                    className="document-detail-comment-btn document-detail-comment-attach"
                    title="Đính kèm file"
                    onClick={handleAttachFile}
                  >
                    <FaPaperclip />
                  </button>
                  <button
                    className="document-detail-comment-btn"
                    title="Gửi bình luận"
                    onClick={handleAddComment}
                    disabled={!newComment.trim()}
                  >
                    <FaPaperPlane />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DocumentDetail;

import React, { useState, useEffect, useRef } from 'react';
import '../../styles/UpdateNote.css';
import closeLoc from '../../assets/closeLoc.svg';
import { validateNoteForm } from '../../utils/validation';
import { showToast } from '../../utils/toastUtils';

const UpdateNote = ({ note, onClose, onUpdate, onDelete }) => {
  const [updatedNote, setUpdatedNote] = useState({
    title: '',
    content: ''
  });
  const [errors, setErrors] = useState({ title: '', content: '' });
  const modalRef = useRef(null);

  useEffect(() => {
    if (note) {
      setUpdatedNote({
        title: note.title,
        content: note.content
      });
    }
  }, [note]);

  const validate = () => {
    const validationErrors = validateNoteForm({ 
      title: updatedNote.title, 
      content: updatedNote.content 
    });
    setErrors(validationErrors);
    return !validationErrors.title && !validationErrors.content;
  };

  const handleUpdate = () => {
    if (validate()) {
      onUpdate(updatedNote);
      onClose();
    }
  };

  const handleDelete = () => {
    onDelete(note.id);
    onClose();
  };

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (modalRef.current && !modalRef.current.contains(event.target)) {
        onClose && onClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [onClose]);

  return (
    <div className="update-note-modal">
      <form className="update-note-form" ref={modalRef} onSubmit={e => { e.preventDefault(); handleUpdate(); }}>
        <div className="update-note-header">
          <h2 className="update-note-title">Chỉnh sửa ghi chú</h2>
          <button type="button" className="update-note-close" onClick={onClose}>
            <img src={closeLoc} alt="Close" />
          </button>
        </div>
        <div className="update-note-input-container">
          {/* <label className="update-note-label">Tạo tiêu đề</label> */}
          <input
            type="text"
            value={updatedNote.title}
            onChange={e => setUpdatedNote({ ...updatedNote, title: e.target.value })}
            placeholder=""
            className={`update-note-input ${errors.title ? 'update-note-input-error' : ''}`}
          />
          {errors.title && <div className="update-note-error-message">{errors.title}</div>}
        </div>
        <div className="update-note-textarea-container">
          {/* <label className="update-note-label">Nhập nội dung</label> */}
          <textarea
            value={updatedNote.content}
            onChange={e => setUpdatedNote({ ...updatedNote, content: e.target.value })}
            placeholder=""
            rows={5}
            className={`update-note-textarea ${errors.content ? 'update-note-input-error' : ''}`}
          />
          {errors.content && <div className="update-note-error-message">{errors.content}</div>}
        </div>
        <div className="update-note-buttons">
          <button type="button" className="update-note-delete" onClick={handleDelete}>
            Xoá ghi chú
          </button>
          <button type="submit" className="update-note-submit">
            Cập nhật ghi chú
          </button>
        </div>
      </form>
    </div>
  );
};

export default UpdateNote;

{"info": {"_postman_id": "9ca33d94-e900-4418-9502-c3adcbb4dc44", "name": "zmanagement", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "40699722", "_collection_link": "https://aa2222-1213.postman.co/workspace/API-DEV~1012fe5b-7e16-4eb0-8362-3e49859440bd/collection/40699722-9ca33d94-e900-4418-9502-c3adcbb4dc44?action=share&source=collection_link&creator=40699722"}, "item": [{"name": "Admin", "item": [{"name": "auth admin", "item": [{"name": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"<EMAIL>\",\r\n    \"password\": \"Admin123!\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/auth/login", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "auth", "login"]}}, "response": []}, {"name": "<PERSON><PERSON><PERSON><PERSON> mật k<PERSON>u", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"<EMAIL>\"\r\n\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/auth/forgot-password", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "auth", "forgot-password"]}}, "response": []}, {"name": "đặt lại mk", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"password\":\"Sanh123@\"\r\n\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/auth/reset-password/55d0f390e5e9f7c7c9b69ff043a03a25c15f47ea76b215da0ed05591a37b6a39", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "auth", "reset-password", "55d0f390e5e9f7c7c9b69ff043a03a25c15f47ea76b215da0ed05591a37b6a39"]}}, "response": []}, {"name": "cậ<PERSON> nh<PERSON>t trang cá nhân", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************.2H9L19Sbn9fdlx3TKzU-BWjv88lP0bE1bf6KgOjA_zE", "type": "string"}]}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "\r\n   {\r\n  \"fullName\": \"<PERSON><PERSON> Tấn Sanh Update\"\r\n}\r\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/auth/profile", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "auth", "profile"]}}, "response": []}, {"name": "cậ<PERSON> nh<PERSON>t avatar", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************.Unsbp2QyKgKLb1AH_OSQrO3Qfn6F7SywLjCD419Xtgc", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text", "disabled": true}], "body": {"mode": "formdata", "formdata": [{"key": "avatar", "type": "file", "src": "postman-cloud:///1f0241a8-94b0-44b0-94f2-30c3f7bb9ba2"}]}, "url": {"raw": "http://localhost:3000/api/auth/update-avatar", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "auth", "update-avatar"]}}, "response": []}, {"name": "Đổi mk", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.6qYUlO48Hz909N7OWfUnTlY-pfv_7UdLGJvm8vYQn6Y", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n\r\n  \"currentPassword\": \"Huynh123@@@\",\r\n  \"newPassword\": \"Huynh123@\",\r\n  \"confirmPassword\": \"Huynh123@\"\r\n\r\n}\r\n\r\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/auth/change-password", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "auth", "change-password"]}}, "response": []}, {"name": "Profile", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.X4rJbUMF0X7kCbBro5bU3dFh5qMKR-MTCdeTCBTmE54", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "formdata", "formdata": []}, "url": {"raw": "http://localhost:3000/api/auth/profile", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "auth", "profile"]}}, "response": []}, {"name": "Profile id", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.X4rJbUMF0X7kCbBro5bU3dFh5qMKR-MTCdeTCBTmE54", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "formdata", "formdata": []}, "url": {"raw": "http://localhost:3000/api/auth/profile/687070664ab99ac8eabf3e68", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "auth", "profile", "687070664ab99ac8eabf3e68"]}}, "response": []}, {"name": "dashboard", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.OikkFAMAjpx2iaKTcsD9NqXxKib6OYj73dwkdazbbMI", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/admin/dashboard", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "admin", "dashboard"]}}, "response": []}, {"name": "b<PERSON>o trì", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.OYu8MKYxT3kGVxDKp3N09b1FVt3fBE2btcABa93N_xs", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n  \"enabled\": false,\r\n  \"message\": \"\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/admin/maintenance/toggle", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "admin", "maintenance", "toggle"]}}, "response": []}, {"name": "<PERSON><PERSON><PERSON> tra", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.OYu8MKYxT3kGVxDKp3N09b1FVt3fBE2btcABa93N_xs", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/admin/maintenance/status", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "admin", "maintenance", "status"]}}, "response": []}, {"name": "thống kê hệ thống", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.OYu8MKYxT3kGVxDKp3N09b1FVt3fBE2btcABa93N_xs", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/admin/system-stats", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "admin", "system-stats"]}}, "response": []}, {"name": "<PERSON><PERSON><PERSON> động", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.OYu8MKYxT3kGVxDKp3N09b1FVt3fBE2btcABa93N_xs", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/admin/activities", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "admin", "activities"]}}, "response": []}]}, {"name": "<PERSON><PERSON><PERSON><PERSON> lý phòng ban", "item": [{"name": "<PERSON><PERSON><PERSON> danh sách phòng ban", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.OYu8MKYxT3kGVxDKp3N09b1FVt3fBE2btcABa93N_xs", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/departments", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "departments"]}}, "response": []}, {"name": "<PERSON><PERSON><PERSON> phòng ban", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.OYu8MKYxT3kGVxDKp3N09b1FVt3fBE2btcABa93N_xs", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n  \"name\": \"Thực tập 3\",\r\n  \"code\": \"TT3\"\r\n  //\"headEmployeeCode\": \"NV002\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/departments", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "departments"]}}, "response": []}, {"name": "<PERSON><PERSON><PERSON> nhật phòng ban", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.OYu8MKYxT3kGVxDKp3N09b1FVt3fBE2btcABa93N_xs", "type": "string"}]}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"name\": \"Thực tập 3 Update\",\r\n  \"code\": \"TT3\",\r\n  \"headEmployeeCode\": \"NV004\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/departments/686f7ae627d52aadcb6c8cf8", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "departments", "686f7ae627d52aadcb6c8cf8"]}}, "response": []}, {"name": "xóa phòng ban", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.OYu8MKYxT3kGVxDKp3N09b1FVt3fBE2btcABa93N_xs", "type": "string"}]}, "method": "DELETE", "header": [], "url": {"raw": "http://localhost:3000/api/departments/686f7ae627d52aadcb6c8cf8", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "departments", "686f7ae627d52aadcb6c8cf8"]}}, "response": []}]}, {"name": "<PERSON><PERSON><PERSON><PERSON> lý ng<PERSON><PERSON> dùng", "item": [{"name": "lấy ds user", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.X4rJbUMF0X7kCbBro5bU3dFh5qMKR-MTCdeTCBTmE54", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/users", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "users"]}}, "response": []}, {"name": "<PERSON><PERSON><PERSON><PERSON> ng<PERSON> dùng", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.X4rJbUMF0X7kCbBro5bU3dFh5qMKR-MTCdeTCBTmE54", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"fullName\": \"<PERSON><PERSON>\",\r\n    \"email\": \"sanhdep<PERSON><PERSON>@gmail.com\",\r\n    \"role\": \"staff\",\r\n  \"position\": \"frontend Developer1\",\r\n  \"departmentCode\": \"TT1\"\r\n}\r\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/users", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "users"]}}, "response": []}, {"name": "<PERSON><PERSON><PERSON> ng<PERSON><PERSON> dùng", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.CtpjN7IUZbbqK_dBgDEeToP_E5dhFHTjIIdb8tN0oT0", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n  \"fullName\": \"testupdate\",\r\n  \"role\": \"staff\",\r\n  \"position\": \"Test 1\"\r\n  \r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/users/users/686f9a4a664433e623c806fe", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "users", "users", "686f9a4a664433e623c806fe"]}}, "response": []}, {"name": "<PERSON><PERSON><PERSON> ho<PERSON> động của user", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.CtpjN7IUZbbqK_dBgDEeToP_E5dhFHTjIIdb8tN0oT0", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/users/users/686f9a4a664433e623c806fe/activities", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "users", "users", "686f9a4a664433e623c806fe", "activities"]}}, "response": []}, {"name": "<PERSON><PERSON> t<PERSON> hàng lo<PERSON>", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.CtpjN7IUZbbqK_dBgDEeToP_E5dhFHTjIIdb8tN0oT0", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n  \"action\": \"block\",\r\n  \"userIds\": [\"686f9a4a664433e623c806fe\"]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/users/users/bulk", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "users", "users", "bulk"]}}, "response": []}, {"name": "Khóa/Mở Khóa", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.CtpjN7IUZbbqK_dBgDEeToP_E5dhFHTjIIdb8tN0oT0", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/users/users/686f9a4a664433e623c806fe/toggle-block", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "users", "users", "686f9a4a664433e623c806fe", "toggle-block"]}}, "response": []}, {"name": "Thay role User", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.CtpjN7IUZbbqK_dBgDEeToP_E5dhFHTjIIdb8tN0oT0", "type": "string"}]}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"newRole\": \"hr\",\r\n  \"reason\": \"Th<PERSON>ng chức\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/users/users/686f9a4a664433e623c806fe/change-role", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "users", "users", "686f9a4a664433e623c806fe", "change-role"]}}, "response": []}, {"name": "Xóa user", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.D26NEwLmlAvWfZrcuf4Agga84BF8mfqZ5QHUbeG5k-4", "type": "string"}]}, "method": "DELETE", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/users/686fce5d2d2e14696319d49b", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "users", "686fce5d2d2e14696319d49b"]}}, "response": []}, {"name": "lấy ds user đã xóa", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.CtpjN7IUZbbqK_dBgDEeToP_E5dhFHTjIIdb8tN0oT0", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/users/deleted", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "users", "deleted"]}}, "response": []}, {"name": "<PERSON><PERSON><PERSON><PERSON> phục người dùng", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.CtpjN7IUZbbqK_dBgDEeToP_E5dhFHTjIIdb8tN0oT0", "type": "string"}]}, "method": "PUT", "header": [], "url": {"raw": "http://localhost:3000/api/users/users/686f9a4a664433e623c806fe/restore", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "users", "users", "686f9a4a664433e623c806fe", "restore"]}}, "response": []}, {"name": "<PERSON><PERSON><PERSON> v<PERSON>nh vi<PERSON><PERSON>(admin có thể khôi phục)", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.CtpjN7IUZbbqK_dBgDEeToP_E5dhFHTjIIdb8tN0oT0", "type": "string"}]}, "method": "DELETE", "header": [], "url": {"raw": "http://localhost:3000/api/users/users/686f9a4a664433e623c806fe/permanent", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "users", "users", "686f9a4a664433e623c806fe", "permanent"]}}, "response": []}, {"name": "xem ds user x<PERSON><PERSON> v<PERSON><PERSON> v<PERSON><PERSON>n", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.CtpjN7IUZbbqK_dBgDEeToP_E5dhFHTjIIdb8tN0oT0", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/admin/users/permanently-deleted", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "admin", "users", "permanently-deleted"]}}, "response": []}, {"name": "kh<PERSON>i phục user đã xóa vĩnh viễn", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.CtpjN7IUZbbqK_dBgDEeToP_E5dhFHTjIIdb8tN0oT0", "type": "string"}]}, "method": "PUT", "header": [], "url": {"raw": "http://localhost:3000/api/admin/users/686f9a4a664433e623c806fe/restore-permanent", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "admin", "users", "686f9a4a664433e623c806fe", "restore-permanent"]}}, "response": []}, {"name": "lấy ds thành viên cũng phòng ban", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.D26NEwLmlAvWfZrcuf4Agga84BF8mfqZ5QHUbeG5k-4", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/users/department/6868a859ba2f43313f4e26b9/members", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "users", "department", "6868a859ba2f43313f4e26b9", "members"]}}, "response": []}, {"name": "lấy ds thành viên cùng dự án", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.D26NEwLmlAvWfZrcuf4Agga84BF8mfqZ5QHUbeG5k-4", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/users/projects/686fec1862ead3ae888c0c87/members", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "users", "projects", "686fec1862ead3ae888c0c87", "members"]}}, "response": []}]}, {"name": "<PERSON><PERSON><PERSON><PERSON> lý d<PERSON>n", "item": [{"name": "<PERSON><PERSON><PERSON> danh sách d<PERSON>n", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoxNzUyMjA1NzY4NzIyLCJpYXQiOjE3NTIyMDU3NjgsImV4cCI6MTc1MjIzNDU2OCwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.8lRpJ1T5jj6w7BToMWRsOj9uCzMuZeA_MfQsio2fI_s", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/projects", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects"]}}, "response": []}, {"name": "l<PERSON>y chi tiết dự án", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoxNzUyMjA1NzY4NzIyLCJpYXQiOjE3NTIyMDU3NjgsImV4cCI6MTc1MjIzNDU2OCwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.8lRpJ1T5jj6w7BToMWRsOj9uCzMuZeA_MfQsio2fI_s", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/projects/686fec1862ead3ae888c0c87", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects", "686fec1862ead3ae888c0c87"]}}, "response": []}, {"name": "tạo dự án", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoxNzUyMjA1NzY4NzIyLCJpYXQiOjE3NTIyMDU3NjgsImV4cCI6MTc1MjIzNDU2OCwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.8lRpJ1T5jj6w7BToMWRsOj9uCzMuZeA_MfQsio2fI_s", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n  \"name\": \"Test7\",\r\n  \"description\": \"test7\",\r\n  \"departmentId\": \"6868a859ba2f43313f4e26b9\",\r\n  \"leaderId\": \"686f9a4a664433e623c806fe\",\r\n  \"status\": \"waiting\",\r\n  \"priority\": \"high\",\r\n  \"startDate\": \"2025-02-01\",\r\n  \"endDate\": \"2025-08-31\",\r\n  \"members\": [\r\n    {\r\n      \"userId\": \"686fe99062ead3ae888c0c48\"\r\n    }\r\n  ],\r\n  \"followers\": [\"686f98f295971bf7bf2e62ff\"]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/projects", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects"]}}, "response": []}, {"name": "sửa d<PERSON>n", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.D26NEwLmlAvWfZrcuf4Agga84BF8mfqZ5QHUbeG5k-4", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n  \"name\": \"<PERSON><PERSON> thống quản lý nhân sự\",\r\n  \"description\": \"update\",\r\n  \"departmentId\": \"686f9a4a664433e623c806fe\",\r\n  \"leaderId\": \"686f9a4a664433e623c806fe\",\r\n  \"status\": \"waiting\",\r\n  \"priority\": \"high\",\r\n  \"startDate\": \"2025-02-01\",\r\n  \"endDate\": \"2025-08-31\",\r\n  \"members\": [\r\n    {\r\n      \"userId\": \"686fce5d2d2e14696319d49b\"\r\n    }\r\n  ],\r\n  \"followers\": [\"686f98f295971bf7bf2e62ff\"]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/projects/686fd0e7a3461d8d61e9bc12", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects", "686fd0e7a3461d8d61e9bc12"]}}, "response": []}, {"name": "<PERSON><PERSON><PERSON> d<PERSON>n", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.D26NEwLmlAvWfZrcuf4Agga84BF8mfqZ5QHUbeG5k-4", "type": "string"}]}, "method": "DELETE", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"reason\": \"Dự án bị hủy\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/projects/686fe81eefc48f312ece3bfb", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects", "686fe81eefc48f312ece3bfb"]}}, "response": []}, {"name": "<PERSON>ó<PERSON> v<PERSON><PERSON> vi<PERSON>n", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.D26NEwLmlAvWfZrcuf4Agga84BF8mfqZ5QHUbeG5k-4", "type": "string"}]}, "method": "DELETE", "header": [], "url": {"raw": "http://localhost:3000/api/projects/686fe81eefc48f312ece3bfb/permanent", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects", "686fe81eefc48f312ece3bfb", "permanent"]}}, "response": []}, {"name": "xem ds đã xóa", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.D26NEwLmlAvWfZrcuf4Agga84BF8mfqZ5QHUbeG5k-4", "type": "string"}]}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "http://localhost:3000/api/projects/deleted", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects", "deleted"]}}, "response": []}, {"name": "<PERSON><PERSON><PERSON><PERSON> phục dự án đã xóa", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.D26NEwLmlAvWfZrcuf4Agga84BF8mfqZ5QHUbeG5k-4", "type": "string"}]}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/projects/686fd0e7a3461d8d61e9bc12/restore", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects", "686fd0e7a3461d8d61e9bc12", "restore"]}}, "response": []}, {"name": "xem ds project xó<PERSON> v<PERSON><PERSON> vi<PERSON>n", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.D26NEwLmlAvWfZrcuf4Agga84BF8mfqZ5QHUbeG5k-4", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/admin/projects/permanently-deleted", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "admin", "projects", "permanently-deleted"]}}, "response": []}, {"name": "khôi phục project đã xóa vĩnh viễn", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.D26NEwLmlAvWfZrcuf4Agga84BF8mfqZ5QHUbeG5k-4", "type": "string"}]}, "method": "PUT", "header": [], "url": {"raw": "http://localhost:3000/api/admin/projects/686fe81eefc48f312ece3bfb/restore-permanent", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "admin", "projects", "686fe81eefc48f312ece3bfb", "restore-permanent"]}}, "response": []}, {"name": "tải file", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.OikkFAMAjpx2iaKTcsD9NqXxKib6OYj73dwkdazbbMI", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "multipart/form-data", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "files", "type": "file", "src": "postman-cloud:///1f05a348-bc4d-4fe0-90dd-c09012026f99"}]}, "url": {"raw": "http://localhost:3000/api/projects/686fe84eefc48f312ece3c22/upload", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects", "686fe84eefc48f312ece3c22", "upload"]}}, "response": []}, {"name": "Xem file", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.OikkFAMAjpx2iaKTcsD9NqXxKib6OYj73dwkdazbbMI", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/projects/686fe84eefc48f312ece3c22/files", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects", "686fe84eefc48f312ece3c22", "files"]}}, "response": []}, {"name": "xóa file", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.OikkFAMAjpx2iaKTcsD9NqXxKib6OYj73dwkdazbbMI", "type": "string"}]}, "method": "DELETE", "header": [{"key": "Content-Type", "value": "multipart/form-data", "type": "text"}], "body": {"mode": "formdata", "formdata": []}, "url": {"raw": "http://localhost:3000/api/projects/686fe84eefc48f312ece3c22/files/6870a9e9766d80861a6191d9", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects", "686fe84eefc48f312ece3c22", "files", "6870a9e9766d80861a6191d9"]}}, "response": []}, {"name": "thêm thành viên vào dự án", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoxNzUyMjA1NzY4NzIyLCJpYXQiOjE3NTIyMDU3NjgsImV4cCI6MTc1MjIzNDU2OCwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.8lRpJ1T5jj6w7BToMWRsOj9uCzMuZeA_MfQsio2fI_s", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"userId\": \"686fce5d2d2e14696319d49b\"\r\n  \r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/projects/686fec1862ead3ae888c0c87/members", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects", "686fec1862ead3ae888c0c87", "members"]}}, "response": []}, {"name": "x<PERSON>a thành viên", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.D26NEwLmlAvWfZrcuf4Agga84BF8mfqZ5QHUbeG5k-4", "type": "string"}]}, "method": "DELETE", "header": [], "url": {"raw": "http://localhost:3000/api/projects/686fec1862ead3ae888c0c87/members/686fce5d2d2e14696319d49b", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects", "686fec1862ead3ae888c0c87", "members", "686fce5d2d2e14696319d49b"]}}, "response": []}, {"name": "lấy ds công việc trong dự án", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.D26NEwLmlAvWfZrcuf4Agga84BF8mfqZ5QHUbeG5k-4", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/projects/686fec1862ead3ae888c0c87/tasks", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects", "686fec1862ead3ae888c0c87", "tasks"]}}, "response": []}, {"name": "tạo cv trong dự án", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoxNzUyMjA1NzY4NzIyLCJpYXQiOjE3NTIyMDU3NjgsImV4cCI6MTc1MjIzNDU2OCwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.8lRpJ1T5jj6w7BToMWRsOj9uCzMuZeA_MfQsio2fI_s", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"title\": \"Test Thông báo\",\r\n  \"description\": \"<PERSON><PERSON> tả công việc mới1\",\r\n  \"assignedToId\": \"686fe99062ead3ae888c0c48\",\r\n  \"priority\": \"high\",\r\n  \"status\": \"pending\",\r\n  \"dueDate\": \"2025-07-15\",\r\n  \"startDate\": \"2025-06-27\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/projects/686fec1862ead3ae888c0c87/tasks", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects", "686fec1862ead3ae888c0c87", "tasks"]}}, "response": []}, {"name": "cậ<PERSON> nh<PERSON>t tiến độ cv", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoxNzUyMjA1NzY4NzIyLCJpYXQiOjE3NTIyMDU3NjgsImV4cCI6MTc1MjIzNDU2OCwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.8lRpJ1T5jj6w7BToMWRsOj9uCzMuZeA_MfQsio2fI_s", "type": "string"}]}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"status\": \"in_progress\",\r\n  \"progress\": 50,\r\n  \"description\": \"<PERSON><PERSON><PERSON> nhật mô tả công việc\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/projects/686fec1862ead3ae888c0c87/tasks/686fed6662ead3ae888c0cbf", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects", "686fec1862ead3ae888c0c87", "tasks", "686fed6662ead3ae888c0cbf"]}}, "response": []}, {"name": "b<PERSON><PERSON> lu<PERSON> task", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoxNzUyMjA1NzY4NzIyLCJpYXQiOjE3NTIyMDU3NjgsImV4cCI6MTc1MjIzNDU2OCwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.8lRpJ1T5jj6w7BToMWRsOj9uCzMuZeA_MfQsio2fI_s", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{ \"content\": \"<PERSON><PERSON> hoàn thành 100% công việc này\"}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/projects/686fec1862ead3ae888c0c87/tasks/68708fd3036b9c5a2280410c/comments", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects", "686fec1862ead3ae888c0c87", "tasks", "68708fd3036b9c5a2280410c", "comments"]}}, "response": []}, {"name": "l<PERSON>y ds bình luận", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoxNzUyMjA1NzY4NzIyLCJpYXQiOjE3NTIyMDU3NjgsImV4cCI6MTc1MjIzNDU2OCwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.8lRpJ1T5jj6w7BToMWRsOj9uCzMuZeA_MfQsio2fI_s", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/projects/686fec1862ead3ae888c0c87/tasks/68708fd3036b9c5a2280410c/comments", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects", "686fec1862ead3ae888c0c87", "tasks", "68708fd3036b9c5a2280410c", "comments"]}}, "response": []}, {"name": "x<PERSON><PERSON> bl", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoxNzUyMjA1NzY4NzIyLCJpYXQiOjE3NTIyMDU3NjgsImV4cCI6MTc1MjIzNDU2OCwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.8lRpJ1T5jj6w7BToMWRsOj9uCzMuZeA_MfQsio2fI_s", "type": "string"}]}, "method": "DELETE", "header": [], "url": {"raw": "http://localhost:3000/api/projects/686fec1862ead3ae888c0c87/tasks/68708fd3036b9c5a2280410c/comments/68708ffd036b9c5a2280411e", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects", "686fec1862ead3ae888c0c87", "tasks", "68708fd3036b9c5a2280410c", "comments", "68708ffd036b9c5a2280411e"]}}, "response": []}, {"name": "xóa task", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoxNzUyMjA1NzY4NzIyLCJpYXQiOjE3NTIyMDU3NjgsImV4cCI6MTc1MjIzNDU2OCwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.8lRpJ1T5jj6w7BToMWRsOj9uCzMuZeA_MfQsio2fI_s", "type": "string"}]}, "method": "DELETE", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/projects/686fec1862ead3ae888c0c87/tasks/68708dcbbd222034828f4c18", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects", "686fec1862ead3ae888c0c87", "tasks", "68708dcbbd222034828f4c18"]}}, "response": []}, {"name": "l<PERSON><PERSON> danh s<PERSON>ch task đã xóa", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoxNzUyMjA1NzY4NzIyLCJpYXQiOjE3NTIyMDU3NjgsImV4cCI6MTc1MjIzNDU2OCwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.8lRpJ1T5jj6w7BToMWRsOj9uCzMuZeA_MfQsio2fI_s", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/projects/686fec1862ead3ae888c0c87/tasks/deleted", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects", "686fec1862ead3ae888c0c87", "tasks", "deleted"]}}, "response": []}, {"name": "<PERSON><PERSON><PERSON><PERSON> phụ<PERSON> task", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoxNzUyMjA1NzY4NzIyLCJpYXQiOjE3NTIyMDU3NjgsImV4cCI6MTc1MjIzNDU2OCwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.8lRpJ1T5jj6w7BToMWRsOj9uCzMuZeA_MfQsio2fI_s", "type": "string"}]}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/projects/686fec1862ead3ae888c0c87/tasks/68708dcbbd222034828f4c18/restore", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects", "686fec1862ead3ae888c0c87", "tasks", "68708dcbbd222034828f4c18", "restore"]}}, "response": []}, {"name": "xóa vv task", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoxNzUyMjA1NzY4NzIyLCJpYXQiOjE3NTIyMDU3NjgsImV4cCI6MTc1MjIzNDU2OCwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.8lRpJ1T5jj6w7BToMWRsOj9uCzMuZeA_MfQsio2fI_s", "type": "string"}]}, "method": "DELETE", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/projects/686fec1862ead3ae888c0c87/tasks/68708dcbbd222034828f4c18/permanent", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects", "686fec1862ead3ae888c0c87", "tasks", "68708dcbbd222034828f4c18", "permanent"]}}, "response": []}, {"name": "<PERSON><PERSON><PERSON><PERSON> kê <PERSON>", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.D26NEwLmlAvWfZrcuf4Agga84BF8mfqZ5QHUbeG5k-4", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/tasks/686fed6662ead3ae888c0cbf/stats/overview", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "tasks", "686fed6662ead3ae888c0cbf", "stats", "overview"]}}, "response": []}]}, {"name": "<PERSON><PERSON><PERSON><PERSON> lý công vi<PERSON>c cá nhân", "item": [{"name": "Tạo cv", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoxNzUyMTk3Mzk4NTM4LCJpYXQiOjE3NTIxOTczOTgsImV4cCI6MTc1MjIyNjE5OCwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.jN_jxHfkzPrv65_KtlyTRO1PFxNRaQm339siUlt4eAI", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n  \"title\": \"Công việc 1\",\r\n  \"description\": \"test 1\"\r\n  \r\n}\r\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/common/personal-tasks", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "common", "personal-tasks"]}}, "response": []}, {"name": "lấy cv", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoxNzUyMTk3Mzk4NTM4LCJpYXQiOjE3NTIxOTczOTgsImV4cCI6MTc1MjIyNjE5OCwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.jN_jxHfkzPrv65_KtlyTRO1PFxNRaQm339siUlt4eAI", "type": "string"}]}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "\r\n{\r\n  \"description\": \"<PERSON><PERSON><PERSON> thành báo cáo tháng - Updated\",\r\n  \"notes\": \"<PERSON><PERSON> thu thập xong số li<PERSON>u, đang tổng hợp\"\r\n}\r\n"}, "url": {"raw": "http://localhost:3000/api/common/personal-tasks", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "common", "personal-tasks"]}}, "response": []}, {"name": "l<PERSON>y cv chi tiết", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoxNzUyMTk3Mzk4NTM4LCJpYXQiOjE3NTIxOTczOTgsImV4cCI6MTc1MjIyNjE5OCwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.jN_jxHfkzPrv65_KtlyTRO1PFxNRaQm339siUlt4eAI", "type": "string"}]}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "\r\n{\r\n  \"description\": \"<PERSON><PERSON><PERSON> thành báo cáo tháng - Updated\",\r\n  \"notes\": \"<PERSON><PERSON> thu thập xong số li<PERSON>u, đang tổng hợp\"\r\n}\r\n"}, "url": {"raw": "http://localhost:3000/api/common/personal-tasks/68706e274ab99ac8eabf3e2d", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "common", "personal-tasks", "68706e274ab99ac8eabf3e2d"]}}, "response": []}, {"name": "sửa cv", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoxNzUyMTk3Mzk4NTM4LCJpYXQiOjE3NTIxOTczOTgsImV4cCI6MTc1MjIyNjE5OCwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.jN_jxHfkzPrv65_KtlyTRO1PFxNRaQm339siUlt4eAI", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "\r\n{\r\n    \"title\":\"Update\",\r\n  \"description\": \"<PERSON><PERSON>n thành báo cáo tháng - Updated\"\r\n\r\n}\r\n"}, "url": {"raw": "http://localhost:3000/api/common/personal-tasks/68706e274ab99ac8eabf3e2d", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "common", "personal-tasks", "68706e274ab99ac8eabf3e2d"]}}, "response": []}, {"name": "Xóa cv cá nhân vào thùng rác", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoxNzUyMTk3Mzk4NTM4LCJpYXQiOjE3NTIxOTczOTgsImV4cCI6MTc1MjIyNjE5OCwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.jN_jxHfkzPrv65_KtlyTRO1PFxNRaQm339siUlt4eAI", "type": "string"}]}, "method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "http://localhost:3000/api/common/personal-tasks/68706e274ab99ac8eabf3e2d", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "common", "personal-tasks", "68706e274ab99ac8eabf3e2d"]}}, "response": []}, {"name": "xem ds thùng r<PERSON>c", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoxNzUyMTk3Mzk4NTM4LCJpYXQiOjE3NTIxOTczOTgsImV4cCI6MTc1MjIyNjE5OCwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.jN_jxHfkzPrv65_KtlyTRO1PFxNRaQm339siUlt4eAI", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/common/personal-tasks/trashed", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "common", "personal-tasks", "trashed"]}}, "response": []}, {"name": "kh<PERSON><PERSON> phục", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************.Ek1RN1-usrKnVyQ-8BiEy4VUpBEuWbOtUfn-2BfcTqU", "type": "string"}]}, "method": "POST", "header": [], "url": {"raw": "http://localhost:3000/api/staff/personal-tasks/6868bac3d6aff09c1d5d01c8/restore", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "staff", "personal-tasks", "6868bac3d6aff09c1d5d01c8", "restore"]}}, "response": []}, {"name": "<PERSON>ó<PERSON> v<PERSON><PERSON> vi<PERSON>n", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoxNzUyMTk3Mzk4NTM4LCJpYXQiOjE3NTIxOTczOTgsImV4cCI6MTc1MjIyNjE5OCwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.jN_jxHfkzPrv65_KtlyTRO1PFxNRaQm339siUlt4eAI", "type": "string"}]}, "method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "http://localhost:3000/api/common/personal-tasks/68706e274ab99ac8eabf3e2d/permanent", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "common", "personal-tasks", "68706e274ab99ac8eabf3e2d", "permanent"]}}, "response": []}]}, {"name": "qu<PERSON><PERSON> lý email", "item": [{"name": "trạng thái email", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.OYu8MKYxT3kGVxDKp3N09b1FVt3fBE2btcABa93N_xs", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/admin/email-status", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "admin", "email-status"]}}, "response": []}, {"name": "retry thủ công", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.OYu8MKYxT3kGVxDKp3N09b1FVt3fBE2btcABa93N_xs", "type": "string"}]}, "method": "POST", "header": [], "url": {"raw": "http://localhost:3000/api/admin/retry-emails", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "admin", "retry-emails"]}}, "response": []}, {"name": "d<PERSON><PERSON> log email", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.OYu8MKYxT3kGVxDKp3N09b1FVt3fBE2btcABa93N_xs", "type": "string"}]}, "method": "DELETE", "header": [], "url": {"raw": "http://localhost:3000/api/admin/clean-email-logs", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "admin", "clean-email-logs"]}}, "response": []}]}, {"name": "t<PERSON><PERSON> k<PERSON>m", "item": [{"name": "tìm kiếm với từ khóa", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoxNzUyMTk3Mzk4NTM4LCJpYXQiOjE3NTIxOTczOTgsImV4cCI6MTc1MjIyNjE5OCwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.jN_jxHfkzPrv65_KtlyTRO1PFxNRaQm339siUlt4eAI", "type": "string"}]}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "\r\n{\r\n  \"description\": \"<PERSON><PERSON><PERSON> thành báo cáo tháng - Updated\",\r\n  \"notes\": \"<PERSON><PERSON> thu thập xong số li<PERSON>u, đang tổng hợp\"\r\n}\r\n"}, "url": {"raw": "http://localhost:3000/api/common/search?query=công việc", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "common", "search"], "query": [{"key": "query", "value": "c<PERSON><PERSON> vi<PERSON>c"}]}}, "response": []}, {"name": "l<PERSON><PERSON> sử tìm kiếm", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoxNzUyMTk3Mzk4NTM4LCJpYXQiOjE3NTIxOTczOTgsImV4cCI6MTc1MjIyNjE5OCwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.jN_jxHfkzPrv65_KtlyTRO1PFxNRaQm339siUlt4eAI", "type": "string"}]}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "\r\n{\r\n  \"description\": \"<PERSON><PERSON><PERSON> thành báo cáo tháng - Updated\",\r\n  \"notes\": \"<PERSON><PERSON> thu thập xong số li<PERSON>u, đang tổng hợp\"\r\n}\r\n"}, "url": {"raw": "http://localhost:3000/api/common/search/history", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "common", "search", "history"]}}, "response": []}, {"name": "tìm kiếm nâng cao", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoxNzUyMTk3Mzk4NTM4LCJpYXQiOjE3NTIxOTczOTgsImV4cCI6MTc1MjIyNjE5OCwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.jN_jxHfkzPrv65_KtlyTRO1PFxNRaQm339siUlt4eAI", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n  \"query\": \"test\",\r\n  \"filters\": {\r\n    \"type\": \"users\",\r\n    \"departmentId\": \"6868a859ba2f43313f4e26b9\" \r\n  },\r\n  \"limit\": 10,\r\n  \"page\": 1,\r\n  \"sortBy\": \"relevance\"\r\n}"}, "url": {"raw": "http://localhost:3000/api/common/search/advanced", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "common", "search", "advanced"]}}, "response": []}, {"name": "x<PERSON><PERSON> lịch sử tìm kiếm", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoxNzUyMTk3Mzk4NTM4LCJpYXQiOjE3NTIxOTczOTgsImV4cCI6MTc1MjIyNjE5OCwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.jN_jxHfkzPrv65_KtlyTRO1PFxNRaQm339siUlt4eAI", "type": "string"}]}, "method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "\r\n{\r\n  \"description\": \"<PERSON><PERSON><PERSON> thành báo cáo tháng - Updated\",\r\n  \"notes\": \"<PERSON><PERSON> thu thập xong số li<PERSON>u, đang tổng hợp\"\r\n}\r\n"}, "url": {"raw": "http://localhost:3000/api/common/search/history", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "common", "search", "history"]}}, "response": []}]}, {"name": "thống kê", "item": [{"name": "<PERSON>h<PERSON><PERSON> kê phòng ban", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.OikkFAMAjpx2iaKTcsD9NqXxKib6OYj73dwkdazbbMI", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/stats/departments", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "stats", "departments"]}}, "response": []}, {"name": "<PERSON>h<PERSON><PERSON> kê người dùng", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.OikkFAMAjpx2iaKTcsD9NqXxKib6OYj73dwkdazbbMI", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/stats/users", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "stats", "users"]}}, "response": []}, {"name": "Thống kê project phòng ban", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.OikkFAMAjpx2iaKTcsD9NqXxKib6OYj73dwkdazbbMI", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/stats/projects-by-department", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "stats", "projects-by-department"]}}, "response": []}, {"name": "Thống kê project tất cả phòng ban", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.OikkFAMAjpx2iaKTcsD9NqXxKib6OYj73dwkdazbbMI", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/stats/all-departments-projects", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "stats", "all-departments-projects"]}}, "response": []}, {"name": "<PERSON><PERSON><PERSON><PERSON> kê <PERSON> đ<PERSON><PERSON><PERSON> giao", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************.c4MHEGYUUmUTu8GjKywJjTNLzQW3WFpsIHnaFhBejyk", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/stats/staff-task-assignments", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "stats", "staff-task-assignments"]}}, "response": []}]}, {"name": "thông báo", "item": [{"name": "all thông báo", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.OikkFAMAjpx2iaKTcsD9NqXxKib6OYj73dwkdazbbMI", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/notification", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "notification"]}}, "response": []}, {"name": "thông b<PERSON>o ch<PERSON><PERSON> đ<PERSON>c", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.OikkFAMAjpx2iaKTcsD9NqXxKib6OYj73dwkdazbbMI", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/notification/unread-count", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "notification", "unread-count"]}}, "response": []}, {"name": "thông báo theo lo<PERSON>i", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.OikkFAMAjpx2iaKTcsD9NqXxKib6OYj73dwkdazbbMI", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "// \"task_assigned\",\r\n//     \"task_completed\",\r\n//     \"task_overdue\",\r\n//     \"project_created\",\r\n//     \"project_updated\",\r\n//     \"project_completed\",\r\n//     \"system\",\r\n//     \"reminder\",\r\n//     \"announcement\",\r\n//     \"other\"", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/notification/type/task_completed", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "notification", "type", "task_completed"]}}, "response": []}, {"name": "đ<PERSON><PERSON> dấu đã đọc", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.OikkFAMAjpx2iaKTcsD9NqXxKib6OYj73dwkdazbbMI", "type": "string"}]}, "method": "PUT", "header": [], "url": {"raw": "http://localhost:3000/api/notification/mark-read/68709cd0a34e47cafe63f03e", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "notification", "mark-read", "68709cd0a34e47cafe63f03e"]}}, "response": []}, {"name": "đ<PERSON><PERSON> dấu đã đọc tất cả", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.OikkFAMAjpx2iaKTcsD9NqXxKib6OYj73dwkdazbbMI", "type": "string"}]}, "method": "PUT", "header": [], "url": {"raw": "http://localhost:3000/api/notification/mark-all-read", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "notification", "mark-all-read"]}}, "response": []}, {"name": "xóa all thông báo đã đọc", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.OikkFAMAjpx2iaKTcsD9NqXxKib6OYj73dwkdazbbMI", "type": "string"}]}, "method": "DELETE", "header": [], "url": {"raw": "http://localhost:3000/api/notification/delete-read/all", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "notification", "delete-read", "all"]}}, "response": []}, {"name": "x<PERSON>a thông b<PERSON><PERSON>", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.OikkFAMAjpx2iaKTcsD9NqXxKib6OYj73dwkdazbbMI", "type": "string"}]}, "method": "DELETE", "header": [], "url": {"raw": "http://localhost:3000/api/notification/68709cd0a34e47cafe63f03e", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "notification", "68709cd0a34e47cafe63f03e"]}}, "response": []}]}]}, {"name": "<PERSON><PERSON><PERSON>p", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"<EMAIL>\",\r\n    \"password\": \"Sanh123@\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/auth/login", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "auth", "login"]}}, "response": []}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"sanh<PERSON><PERSON><PERSON><PERSON>@gmail.com\",\r\n    \"password\": \"Hts12345@\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/auth/login", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "auth", "login"]}}, "response": []}]}
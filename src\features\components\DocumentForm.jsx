import React, { useState, useEffect, useRef } from 'react';
import '../../styles/DocumentForm.css';
import closePanelIcon from '../../assets/closePanel.svg';
import loadfileIcon from '../../assets/loadfile.svg';
import userIcon from '../../assets/user1.png';
import { FaPaperPlane, FaPaperclip } from 'react-icons/fa';
import { createDocument, uploadDocumentFile, uploadProjectFile } from '../../api/documents';
import { useParams } from 'react-router-dom';
import { getProjectById } from '../../api/projectManagement';

const getCurrentUser = () => {
  try {
    const userRaw = JSON.parse(localStorage.getItem('user') || '{}');
    return userRaw.user || userRaw;
  } catch {
    return {};
  }
};

const DocumentForm = ({ onClose, onSubmit }) => {
  const { projectId } = useParams();
  const [visible, setVisible] = useState(false);
  const [formData, setFormData] = useState({
    creator: '',
    project: '', // sẽ là projectId
    createdDate: '',
    lastUpdated: '',
    description: '',
    attachments: []
  });
  const [projectName, setProjectName] = useState('');
  const [selectedFiles, setSelectedFiles] = useState([]);
  const [comment, setComment] = useState('');
  const fileInputRef = useRef(null);
  const commentFileInputRef = useRef(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);

  useEffect(() => {
    // Set current date as default
    const currentDate = new Date().toISOString().split('T')[0];
    // Lấy user hiện tại
    const user = getCurrentUser();
    setFormData(prev => ({
      ...prev,
      creator: user.fullName || user.name || 'Chưa có tên',
      project: projectId || '',
      createdDate: currentDate, // luôn là ngày hiện tại
      lastUpdated: currentDate
    }));
    // Lấy tên dự án
    if (projectId) {
      getProjectById(projectId).then(res => {
        setProjectName(res.data?.name || '');
      }).catch(() => setProjectName(''));
    }
    setTimeout(() => setVisible(true), 10);
  }, [projectId]);

  const handleOverlayClick = (e) => {
    if (e.target.classList.contains('document-form-overlay')) {
      handleClose();
    }
  };

  const handleClose = () => {
    setVisible(false);
    setTimeout(() => onClose && onClose(), 250);
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleFileSelect = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = (e) => {
    const files = Array.from(e.target.files);
    setSelectedFiles(prev => [...prev, ...files]);
  };

  const removeFile = (index) => {
    setSelectedFiles(prev => prev.filter((_, i) => i !== index));
  };

  const handleSubmit = async (e) => {
    if (e) e.preventDefault();
    setLoading(true);
    setError('');
    setSuccess(false);
    try {
      if (!formData.project) {
        setError('Không xác định được dự án.');
        setLoading(false);
        return;
      }
      if (selectedFiles.length === 0) {
        setError('Vui lòng chọn ít nhất một tệp để upload.');
        setLoading(false);
        return;
      }
      // Upload từng file lên project
      for (const file of selectedFiles) {
        await uploadProjectFile(formData.project, file);
      }

      setSuccess(true);

      // Gọi callback để parent component refresh danh sách
      if (onSubmit) {
        await onSubmit(formData);
      }

      setTimeout(() => handleClose(), 1000);
    } catch (err) {
      setError(err.message || 'Có lỗi xảy ra khi upload file.');
    } finally {
      setLoading(false);
    }
  };

  // Xử lý khi nhấn nút đính kèm file trong comment
  const handleCommentAttachFile = () => {
    commentFileInputRef.current.click();
  };

  // Xử lý khi chọn file đính kèm trong comment
  const handleCommentFileSelected = (e) => {
    const files = Array.from(e.target.files);
    if (files.length > 0) {
      // Thêm files vào selectedFiles
      setSelectedFiles(prev => [...prev, ...files]);

      // Reset input file
      e.target.value = '';
    }
  };

  // Xử lý nhấn Enter để gửi comment
  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      // Có thể thêm logic xử lý comment ở đây nếu cần
    }
  };

  return (
    <div className="document-form-overlay" onClick={handleOverlayClick}>
      <div className={`document-form-container${visible ? ' show' : ''}`}>
        <button className="document-form-close-btn" onClick={handleClose} title="Đóng">
          <img src={closePanelIcon} alt="Đóng" />
        </button>
        
        {/* Header cố định */}
        <div className="document-form-header">
          <h3>Tài liệu hướng dẫn sử dụng trong dự án</h3>
        </div>

        <div className="document-form-content">
          <form onSubmit={handleSubmit} className="document-form">
            <div className="form-row">
              <label className="form-label">Người tạo</label>
              <div className="form-value">
                <img src={getCurrentUser().avatar || userIcon} alt="User" className="user-avatar" />
                <span>{formData.creator}</span>
              </div>
            </div>

            <div className="form-row">
              <label className="form-label">Dự án</label>
              <div className="form-value">
                <span>{projectName || '...'}</span>
              </div>
            </div>

            <div className="form-row">
              <label className="form-label">Ngày tạo</label>
              <div className="form-value">
                <input
                  type="date"
                  name="createdDate"
                  value={formData.createdDate}
                  className="form-input date-input"
                  disabled
                />
              </div>
            </div>



            <div className="form-row">
              <label className="form-label">Mô tả</label>
              <div className="form-value">
                <textarea
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  placeholder="Nhập mô tả..."
                  className="form-textarea"
                  rows="3"
                />
              </div>
            </div>

            <div className="form-row file-upload-row">
              <label className="form-label">Tệp đính kèm</label>
              <div className="form-value">
                <div className="file-upload-section">
                  {selectedFiles.length === 0 ? (
                    <button
                      type="button"
                      onClick={handleFileSelect}
                      className="file-upload-area"
                    >
                      <img src={loadfileIcon} alt="Upload" className="loadfile-icon" />
                      <span className="upload-text">Bấm vào để tải lên tệp</span>
                    </button>
                  ) : (
                    <div className="files-container">
                      <button
                        type="button"
                        onClick={handleFileSelect}
                        className="add-more-files-btn"
                      >
                        <img src={loadfileIcon} alt="Upload" className="loadfile-icon-small" />
                        <span>Thêm tệp</span>
                      </button>
                      <div className="selected-files">
                        {selectedFiles.map((file, index) => (
                          <div key={index} className="file-item">
                            <span className="file-name">{file.name}</span>
                            <button
                              type="button"
                              onClick={() => removeFile(index)}
                              className="remove-file-btn"
                            >
                              ×
                            </button>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                  <input
                    ref={fileInputRef}
                    type="file"
                    multiple
                    onChange={handleFileChange}
                    className="file-input-hidden"
                  />
                </div>
              </div>
            </div>
            {error && <div className="form-error">{error}</div>}
            {success && <div className="form-success">Tạo tài liệu thành công!</div>}
            {loading && <div className="form-loading">Đang xử lý...</div>}
          </form>
        </div>

        {/* Comment footer cố định */}
        <div className="document-form-footer">
          <div className="detail-row detail-row-comment">
            <div className="detail-comment-box-wrapper" style={{paddingRight: 16}}>
              <div className="detailform-comment-box">
                <input
                  className="detail-comment-input"
                  placeholder="Viết bình luận cho bạn"
                  value={comment}
                  onChange={(e) => setComment(e.target.value)}
                  onKeyPress={handleKeyPress}
                />
                <div className="detail-comment-actions">
                  <input
                    type="file"
                    ref={commentFileInputRef}
                    style={{ display: 'none' }}
                    multiple
                    onChange={handleCommentFileSelected}
                  />
                  <button className="detail-comment-attach" title="Đính kèm file" onClick={handleCommentAttachFile}>
                    <FaPaperclip />
                  </button>
                  <button
                    className="detail-comment-btn"
                    title="Gửi bình luận"
                    disabled={!comment.trim()}
                  >
                    <FaPaperPlane />
                  </button>
                </div>
              </div>
            </div>
          </div>

          <div className="form-actions">
            <button type="button" onClick={handleClose} className="btn-cancel" disabled={loading}>
              Hủy
            </button>
            <button type="button" onClick={handleSubmit} className="btn-submit" disabled={loading}>
              {loading ? 'Đang lưu...' : 'Lưu'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DocumentForm;

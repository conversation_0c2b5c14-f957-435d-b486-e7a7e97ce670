@import url('../index.css');

/* Document Detail Container */
.document-detail-container {
  position: fixed;
  top: 0; /* <PERSON><PERSON> sửa: bắt đầu từ đỉnh màn hình, che cả topbar */
  right: 0;
  height: 100vh; /* Đ<PERSON> sửa: chi<PERSON>u cao full màn hình */
  width: 420px;
  max-height: 100vh;
  background: #F6F6F6;
  box-shadow: -2px 0 16px 0 rgba(60, 72, 88, 0.10);
  padding: 0;
  font-size: 15px;
  color: #222;
  z-index: 1000; /* Đ<PERSON> sửa: tăng z-index để hiển thị trên topbar */
  display: flex;
  flex-direction: column;
  transform: translateX(100%);
  opacity: 0;
  transition: transform 0.25s cubic-bezier(.4, 0, .2, 1), opacity 0.18s;
  overflow: hidden;
}

.document-detail-content {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 20px 20px 20px 20px;
  scrollbar-width: thin;
  scrollbar-color: transparent transparent;
  transition: scrollbar-color 0.3s ease;
}

.document-detail-content:hover {
  scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
}

/* Webkit (Chrome, Safari, Edge) */
.document-detail-content::-webkit-scrollbar {
  width: 6px;
}

.document-detail-content::-webkit-scrollbar-track {
  background: transparent;
}

.document-detail-content::-webkit-scrollbar-thumb {
  background-color: transparent;
  border-radius: 6px;
  transition: background-color 0.3s ease;
}

.document-detail-content:hover::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
}

.document-comment-footer {
  position: sticky;
  bottom: 0;
  border-top: 1px solid #eaeaea;
  padding: 12px 20px 12px 20px;
  width: 100%;
  z-index: 5;
  flex-shrink: 0;
  background: #F6F6F6; /* Đảm bảo có background giống container */
  box-sizing: border-box;
}

.document-detail-container.show {
  transform: translateX(0);
  opacity: 1;
}

/* Header cố định */
.document-detail-header {
  position: sticky;
  top: 0;
  border-bottom: 1px solid #eaeaea;
  padding: 30px 20px 16px 20px; /* Tăng padding-top để tránh bị che bởi thanh trạng thái trên mobile */
  z-index: 10;
  flex-shrink: 0;
  background: #F6F6F6; /* Đảm bảo header có background giống container */
  border-radius: 14px 0 0 0; /* Bo góc trên bên trái */
}

.document-detail-header h3 {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  color: #222;
  line-height: 1.4;
  padding-right: 50px; /* Để tránh chồng lên nút đóng */
}

.document-detail-row {
  display: flex;
  align-items: flex-start;
  margin: 16px 0; /* Tăng margin để tạo khoảng cách đồng đều */
  gap: 16px; /* Tăng gap để cân đối hơn */
  padding: 0; /* Đảm bảo không có padding thừa */
}

.document-detail-label {
  min-width: 120px; /* Tăng width để các label dài hơn không bị wrap */
  color: #5b5b5b;
  font-size: 14px;
  font-weight: 500;
  flex-shrink: 0;
  line-height: 1.4; /* Đồng đều line-height */
  padding-top: 2px; /* Căn chỉnh với content */
  display: flex;
  align-items: center;
  gap: 6px;
}

.document-detail-value {
  flex: 1;
  color: #5b5b5b;
  font-size: 15px;
  display: flex;
  align-items: flex-start;
  gap: 6px;
  line-height: 1.4; /* Đồng đều line-height */
  min-height: 20px; /* Đảm bảo chiều cao tối thiểu */
}

.document-user-avatar {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #fff;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);
  flex-shrink: 0; /* Không cho phép avatar bị co lại */
}

.document-detail-row-comment {
  margin: 16px 0; /* Đồng đều với các row khác */
}

/* Close Button */
.document-detail-close-btn {
  position: absolute;
  top: 30px; /* Tăng top để phù hợp với padding-top của header */
  right: 20px;
  width: 24px;
  height: 24px;
  background: none;
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background 0.18s;
  z-index: 101;
  padding: 0;
}

.document-detail-close-btn:hover {
  background: #f1f3f4;
}

.document-detail-close-btn img {
  width: 20px;
  height: 20px;
}





/* Description Section */
.document-description-section {
  align-items: flex-start !important;
}

.document-description-display {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  width: 100%;
}

.document-description-display span {
  flex: 1;
  line-height: 1.5;
}

.document-edit-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 2px;
  border-radius: 4px;
  transition: background 0.2s;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.document-edit-btn:hover {
  background: #f1f3f4;
}

.document-edit-btn img {
  width: 16px;
  height: 16px;
  opacity: 0.6;
}

.document-description-edit {
  width: 100%;
}

.document-description-textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  font-size: 14px;
  color: #222;
  background: #fff;
  resize: vertical;
  min-height: 80px;
  font-family: inherit;
  transition: border-color 0.2s, box-shadow 0.2s;
  margin-bottom: 8px;
}

.document-description-textarea:focus {
  outline: none;
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.document-description-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.document-btn-save, .document-btn-cancel {
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
}

.document-btn-save {
  background: #2563eb;
  color: white;
  border: none;
}

.document-btn-save:hover {
  background: #1d4ed8;
}

.document-btn-cancel {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.document-btn-cancel:hover {
  background: #e5e7eb;
}

/* Attachment Section */
.attachment-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #f9fafb;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.file-icon {
  width: 16px;
  height: 16px;
  opacity: 0.6;
}

.attachment-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.attachment-name {
  font-size: 14px;
  color: #374151;
  font-weight: 500;
}

.attachment-date {
  font-size: 12px;
  color: #6b7280;
}

/* Comments Section */
.comments-section {
  align-items: flex-start !important;
}

.comments-section .detail-value {
  flex-direction: column;
  align-items: stretch !important;
  gap: 16px;
}

.comments-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-height: 300px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: transparent transparent;
}

.comments-list:hover {
  scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
}

.comment-item {
  display: flex;
  gap: 8px;
  align-items: flex-start;
}

.comment-avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  object-fit: cover;
  flex-shrink: 0;
}

.comment-content {
  flex: 1;
}

.comment-header {
  margin-bottom: 4px;
}

.comment-user {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.comment-text {
  font-size: 14px;
  color: #6b7280;
  line-height: 1.4;
}

/* Comment Footer cố định */
.document-comment-footer {
  position: sticky;
  bottom: 0;
  border-top: 1px solid #eaeaea;
  padding: 12px 20px;
  width: 100%;
  z-index: 5;
  flex-shrink: 0;
}

/* Comment Input */
.comment-input-section {
  width: 100%;
}

.comment-input-wrapper {
  display: flex;
  align-items: center;
  background: #fafbfc;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 8px 12px;
  gap: 8px;
}

.comment-input {
  flex: 1;
  border: none;
  background: transparent;
  font-size: 14px;
  color: #222;
  outline: none;
}

.comment-input::placeholder {
  color: #9ca3af;
}

.comment-send-btn {
  background: none;
  border: none;
  color: #6b7280;
  font-size: 16px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background 0.2s;
}

.comment-send-btn:hover {
  background: #f1f3f4;
  color: #2563eb;
}

/* CSS cho comment box giống JobDetail */
.detail-row {
  display: flex;
  align-items: flex-start;
  margin: 16px 0;
}

.detail-row input.detail-comment-input {
  flex: 1;
  border-radius: 8px;
  padding: 8px 12px;
  font-size: 15px;
  margin-top: 4px;
  outline: none;
  transition: border 0.2s;
  border: none;
  background: transparent;
  height: 36px;
}

.detailfile-comment-box {
  background: #fafbfc;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  padding: 0 12px;
  display: flex;
  align-items: center;
  gap: 94px;
  min-height: 38px;
  height: 38px;
  box-shadow: none;
  width: 100%;
}

.detail-comment-actions {
  display: flex;
  align-items: center;
  gap: 2px;
}

.detail-comment-btn {
  background: none;
  color: #757575;
  width: 28px;
  height: 28px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  font-size: 18px;
  margin: 0;
  cursor: pointer;
  transition: background 0.2s;
}

.detail-comment-btn:hover {
  background: #f1f3f4;
}

.detail-comment-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.detail-comment-btn:disabled:hover {
  background: none;
}

/* Tách biệt bình luận */
.detail-row-comment {
  margin-top: 0;
  margin-bottom: 0;
}

/* CSS cho attachment section giống JobDetail */
.attachment-section .detail-value {
  flex-direction: column;
  width: 100%;
  max-height: 180px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: transparent transparent;
  transition: scrollbar-color 0.3s ease;
}

.attachment-section .detail-value:hover {
  scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
}

.attachment-section .detail-value::-webkit-scrollbar {
  width: 4px;
}

.attachment-section .detail-value::-webkit-scrollbar-track {
  background: transparent;
}

.attachment-section .detail-value::-webkit-scrollbar-thumb {
  background-color: transparent;
  border-radius: 4px;
  transition: background-color 0.3s ease;
}

.attachment-section .detail-value:hover::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
}

/* Attachment styles giống JobDetail */
.detail-attachment-item {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  padding: 6px 8px;
  border-radius: 6px;
  width: 100%;
  cursor: pointer;
  transition: background-color 0.2s;
}

.detail-attachment-item:hover {
  background-color: #f5f5f5;
}

.detail-attachment-icon {
  margin-right: 8px;
}

.detail-attachment-name {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.detail-attachment-date {
  color: #aaa;
  margin-left: 8px;
  font-size: 12px;
}

.detail-attachment-download {
  color: #1a73e8;
  margin-left: 8px;
  opacity: 0;
  transition: opacity 0.2s;
}

.detail-attachment-item:hover .detail-attachment-download {
  opacity: 1;
}

/* CSS cho activity section giống JobDetail */
.detail-row-activity {
  flex-direction: column;
  align-items: flex-start;
  margin-top: 20px;
  margin-bottom: -20px;
}

/* Override cho activity row value */
.document-detail-row-activity .document-detail-value {
  width: 100%;
  display: block;
  box-sizing: border-box;
  position: relative;
}

.document-detail-activity-list {
  margin-bottom: 18px;
  max-height: 240px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: transparent transparent;
  transition: scrollbar-color 0.3s ease;
}

.document-detail-activity-list:hover {
  scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
}



.document-detail-activity-list::-webkit-scrollbar {
  width: 4px;
}

.document-detail-activity-list::-webkit-scrollbar-track {
  background: transparent;
}

.document-detail-activity-list::-webkit-scrollbar-thumb {
  background-color: transparent;
  border-radius: 4px;
  transition: background-color 0.3s ease;
}

.document-detail-activity-list:hover::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
}

.document-detail-activity-item {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  margin-top: 8px;
  width: calc(100% - 15px);
  box-sizing: border-box;
}

.document-detail-activity-avatar {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  object-fit: cover;
  box-shadow: 0 1px 4px rgba(0,0,0,0.04);
  background: #f5f5f5;
  margin-top: 2px;
}

.document-detail-activity-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  width: calc(100% - 30px);
  overflow-wrap: break-word;
  word-break: break-word;
  box-sizing: border-box;
  max-width: 310px;
}

.document-detail-activity-name {
  font-weight: 600;
  color: #5D5D5D;
  font-size: 14px;
  margin-bottom: 2px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.document-detail-activity-text {
  color: #5D5D5D;
  font-size: 14px;
  line-height: 1.5;
}

.document-detail-activity-time {
  color: #9e9e9e;
  font-size: 12px;
  margin-top: 2px;
}

/* Comment Section */
.document-detail-comment-box-wrapper {
  width: 100%;
  box-sizing: border-box;
}

.document-detailfile-comment-box {
  background: #fafbfc;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  padding: 0 12px;
  display: flex;
  align-items: center;
  gap: 8px;
  min-height: 38px;
  height: 38px;
  box-shadow: none;
  width: calc(100% - 24px); /* Trừ đi padding của wrapper */
  max-width: 100%;
}

.document-detail-comment-input {
  flex: 1;
  border: none;
  outline: none;
  font-size: 14px;
  color: #333;
  background: transparent;
  min-width: 0; /* Đảm bảo input có thể co lại */
}

.document-detail-comment-input::placeholder {
  color: #999;
}

.document-detail-comment-actions {
  display: flex;
  align-items: center;
  gap: 4px;
  flex-shrink: 0; /* Không cho phép actions bị co lại */
  margin-left: auto; /* Đẩy actions về bên phải */
}

.document-detail-comment-btn, .document-detail-comment-attach {
  background: none;
  color: #757575;
  width: 28px;
  height: 28px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  font-size: 18px;
  margin: 0;
  cursor: pointer;
}

.document-detail-comment-btn:hover, .document-detail-comment-attach:hover {
  background: #f1f3f4;
}

.document-detail-comment-btn:disabled {
  color: #ccc;
  cursor: not-allowed;
}

/* Tách biệt hoạt động và bình luận */
.document-detail-row-activity {
  flex-direction: column;
  align-items: flex-start;
  margin-top: 20px;
  margin-bottom: -20px;
}

import { useState, useEffect } from 'react';
import { NOTIFICATION_ENDPOINTS } from '../../../api/endpoints';

const getToken = () => {
  return localStorage.getItem('token') || '';
};

const ProjectNoti = ({ globalFilter = 'all' }) => {
  const [notifications, setNotifications] = useState([]);
  const [loading, setLoading] = useState(false);

  // Fetch notifications from API
  const fetchNotifications = async () => {
    setLoading(true);
    try {
      const token = getToken();
      const res = await fetch(NOTIFICATION_ENDPOINTS.ALL, {
        headers: { Authorization: `Bearer ${token}` },
      });
      const dataRaw = await res.json();
      let data = dataRaw?.data || dataRaw || [];
      // Filter only project notifications
      data = data.filter(noti => [
        'project_created',
        'project_updated',
        'project_completed'
      ].includes(noti.type));
      // Apply globalFilter
      if (globalFilter === 'unread') {
        data = data.filter(noti => !noti.isRead);
      } else if (globalFilter === 'read') {
        data = data.filter(noti => noti.isRead);
      }
      setNotifications(data);
    } catch (err) {
      setNotifications([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchNotifications();
    // eslint-disable-next-line
  }, [globalFilter]);

  const handleMarkAsRead = async (notificationId) => {
    if (!notificationId || typeof notificationId !== 'string') {
      console.error('Invalid notificationId:', notificationId);
      return;
    }
    try {
      const token = getToken();
      const res = await fetch(NOTIFICATION_ENDPOINTS.MARK_READ(notificationId), {
        method: 'PUT',
        headers: {
          Authorization: `Bearer ${token}`
        }
        // Không gửi body, không gửi Content-Type
      });
      const data = await res.json().catch(() => ({}));
      // Mark as read response
      fetchNotifications();
    } catch (err) {
      // handle error if needed
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'high': return '#ff4757';
      case 'medium': return '#ffa502';
      case 'low': return '#2ed573';
      default: return '#747d8c';
    }
  };

  const getTypeIcon = (type) => {
    switch (type) {
      case 'project_created': return '🆕';
      case 'deadline_changed': return '📅';
      case 'project_completed': return '🎉';
      case 'member_added': return '👥';
      case 'progress_update': return '📊';
      case 'budget_updated': return '💰';
      case 'project_paused': return '⏸️';
      default: return '📁';
    }
  };

  const formatCurrency = (amount) => {
    return amount?.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  };

  const formatDate = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    const datePart = date.toLocaleDateString('vi-VN', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    });
    const timePart = date.toLocaleTimeString('vi-VN', {
      hour: '2-digit',
      minute: '2-digit',
    });
    return `${datePart} ${timePart}`;
  };

  return (
    <div className="project-notifications">
      {/* Notifications list */}
      <div className="notifications-list">
        {loading ? (
          <div className="no-notifications"><p>Đang tải thông báo...</p></div>
        ) : notifications.length === 0 ? (
          <div className="no-notifications">
            <p>Không có thông báo dự án nào</p>
          </div>
        ) : (
          notifications.map(notification => (
            <div
              key={notification.id}
              className={`notification-item-simple ${notification.isRead ? 'read' : 'unread'}`}
              onClick={() => handleMarkAsRead(notification.id)}
            >
              <div className="notification-left-border" style={{ background: getPriorityColor(notification.priority) }}></div>
              <div className="notification-simple-content">
                <div className="notification-sender">
                  {notification.createdBy?.fullName || notification.createdBy?.name || notification.changedBy?.fullName || notification.changedBy?.name || notification.sender || 'Hệ thống'}
                </div>
                <div className="notification-text">
                  {getTypeIcon(notification.type)} {notification.message || notification.content} • <span className="notification-simple-time">{formatDate(notification.time || notification.createdAt)}</span>
                </div>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default ProjectNoti;
import React from 'react';
import '../../styles/ConfirmDeletePopup.css';

const ConfirmDeletePopup = ({ isOpen, onConfirm, onCancel, fileName }) => {
  if (!isOpen) return null;

  const handleOverlayClick = (e) => {
    if (e.target.classList.contains('confirm-delete-overlay')) {
      onCancel();
    }
  };

  return (
    <div className="confirm-delete-overlay" onClick={handleOverlayClick}>
      <div className="confirm-delete-popup">
        <div className="confirm-delete-header">
          <h3>Xác nhận xóa tệp</h3>
        </div>
        <div className="confirm-delete-content">
          <p>Bạn có chắc chắn muốn xóa tệp <strong>"{fileName}"</strong> không?</p>
          <p className="confirm-delete-warning">Hành động này không thể hoàn tác.</p>
        </div>
        <div className="confirm-delete-actions">
          <button className="confirm-delete-cancel" onClick={onCancel}>
            Hủy
          </button>
          <button className="confirm-delete-confirm" onClick={onConfirm}>
            Xóa
          </button>
        </div>
      </div>
    </div>
  );
};

export default ConfirmDeletePopup;

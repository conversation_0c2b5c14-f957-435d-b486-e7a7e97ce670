import React, { useState, useEffect } from "react";
// Removed Sidebar and Topbar imports as they're now handled by DashboardLayout
import StatisticalCard from "../../components/StatisticalCard";
import fileIcon from "../../../assets/file-text.svg";
import chartIcon from "../../../assets/chart-column-decreasing.svg";
import userGroupIcon from "../../../assets/users.svg";
import filterIcon from "../../../assets/filter.svg";
import downloadIcon from "../../../assets/download.svg";
import * as XLSX from "xlsx";
import jsPDF from "jspdf";
import { saveAs } from "file-saver";
import { getAllProjects } from "../../../api/projectManagement";
import { getProjectTasks } from "../../../api/taskManagement";

// Cache để tránh gọi API nhiều lần
let renderCache = null;
let cacheTimestamp = 0;
const CACHE_DURATION = 10000; // Giảm xuống 10 giây để load nhanh hơn

// Helper format date
const formatDate = (dateStr) => {
  if (!dateStr) return '';
  
  // If it's already in DD/MM/YYYY format, return as is
  if (typeof dateStr === 'string' && /^\d{1,2}\/\d{1,2}\/\d{4}$/.test(dateStr)) {
    return dateStr;
  }
  
  const d = new Date(dateStr);
  if (isNaN(d)) return dateStr;
  
  // Format to DD/MM/YYYY
  const day = d.getDate();
  const month = d.getMonth() + 1;
  const year = d.getFullYear();
  
  return `${day}/${month}/${year}`;
};
// Helper format mã dự án
const formatProjectCode = (project) => {
  if (!project) return '';
  if (project.projectCode) return project.projectCode;
  const str = String(project.id || project._id || '');
  return 'PRJ-' + str.slice(-6);
};

const Render = () => {
  const [projectProgress, setProjectProgress] = useState([]);
  const [workStats, setWorkStats] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    async function fetchData() {
      // Kiểm tra cache trước
      const now = Date.now();
      if (renderCache && (now - cacheTimestamp) < CACHE_DURATION) {
        setProjectProgress(renderCache.projectProgress);
        setWorkStats(renderCache.workStats);
        setLoading(false);
        return;
      }
      // Nếu không có cache, mới set loading=true
      setLoading(true);
      setError(null);
      try {
        const projectsRes = await getAllProjects();
        const projects = projectsRes.data || [];
        
        // Hiển thị dữ liệu cơ bản ngay lập tức
        if (projects.length > 0) {
          const initialProjectProgress = projects.map(project => ({
            projectName: project.name,
            projectCode: formatProjectCode(project),
            percent: project.progress || 0,
            time: project.startDate && project.endDate
              ? `${formatDate(project.startDate)} - ${formatDate(project.endDate)}`
              : "",
            status: project.status === 'completed' ? 'Hoàn thành' : 
                   project.status === 'in-progress' ? 'Đang tiến hành' : 
                   project.status === 'waiting' ? 'Đang chờ' : 'Chưa có công việc',
          }));
          
          setProjectProgress(initialProjectProgress);
          setLoading(false);
        }
        
        // Fetch all project tasks concurrently
        const projectTasksPromises = projects.map(async (project) => {
          try {
            const tasksRes = await getProjectTasks(project.id || project._id);
            return {
              project,
              tasks: tasksRes.data || []
            };
          } catch (err) {
            console.warn(`Error fetching tasks for project ${project.id}:`, err);
            return {
              project,
              tasks: []
            };
          }
        });

        const projectTasksResults = await Promise.all(projectTasksPromises);
        
        let projectProgressArr = [];
        let allTasks = [];
        
        projectTasksResults.forEach(({ project, tasks }) => {
          allTasks = allTasks.concat(tasks);
          
          // Tính phần trăm tiến độ dự án
          let percent = 0;
          if (tasks.length > 0) {
            if (tasks[0].progress !== undefined) {
              percent = Math.round(
                tasks.reduce((sum, t) => sum + (t.progress || 0), 0) / tasks.length
              );
            } else {
              const completed = tasks.filter((t) => t.status === "completed").length;
              percent = Math.round((completed / tasks.length) * 100);
            }
          }
          
          // Lấy trạng thái dự án
          let status = "Chưa có công việc";
          if (tasks.length > 0) {
            if (tasks.some((t) => t.status === "overdue")) status = "Quá hạn";
            else if (tasks.some((t) => t.status === "in_progress")) status = "Đang tiến hành";
            else if (tasks.some((t) => t.status === "review")) status = "Xem xét";
            else if (tasks.every((t) => t.status === "completed")) status = "Hoàn thành";
            else if (tasks.every((t) => t.status === "pending")) status = "Đang chờ";
            else status = "Đang tiến hành";
          }
          
          projectProgressArr.push({
            projectName: project.name,
            projectCode: formatProjectCode(project),
            percent,
            time: project.startDate && project.endDate
              ? `${formatDate(project.startDate)} - ${formatDate(project.endDate)}`
              : "",
            status,
          });
        });

        // Thống kê trạng thái công việc toàn hệ thống
        const statusLabels = [
          { label: "Hoàn thành", value: "completed" },
          { label: "Đang chờ", value: "pending" },
          { label: "Đang tiến hành", value: "in_progress" },
          { label: "Quá hạn", value: "overdue" },
          { label: "Xem xét", value: "review" },
        ];
        const workStatsArr = statusLabels.map((s) => ({
          label: s.label,
          value: allTasks.filter((t) => t.status === s.value).length,
        }));

        // Lưu vào cache
        renderCache = {
          projectProgress: projectProgressArr,
          workStats: workStatsArr
        };
        cacheTimestamp = now;

        setProjectProgress(projectProgressArr);
        setWorkStats(workStatsArr);
      } catch (err) {
        console.warn('Error fetching render data:', err);
        setProjectProgress([]);
        setWorkStats([]);
        setError('Lỗi khi tải dữ liệu báo cáo');
      } finally {
        setLoading(false);
      }
    }
    fetchData();
  }, [window.location.pathname]);

  const handleFilter = () => {};
  const handleExport = (type) => {
    if (type === "excel") {
      // Gộp dữ liệu báo cáo tiến độ dự án và thống kê công việc
      const projectSheet = projectProgress.map((item) => ({
        "Tên dự án": item.projectName,
        "Mã dự án": item.projectCode,
        "Tiến độ (%)": item.percent,
        "Thời gian": item.time,
        "Trạng thái": item.status,
      }));
      const workSheet = workStats.map((item) => ({
        "Trạng thái": item.label,
        "Số lượng": item.value,
      }));
      const wb = XLSX.utils.book_new();
      const ws1 = XLSX.utils.json_to_sheet(projectSheet);
      XLSX.utils.book_append_sheet(wb, ws1, "TienDoDuAn");
      const ws2 = XLSX.utils.json_to_sheet(workSheet);
      XLSX.utils.book_append_sheet(wb, ws2, "ThongKeCongViec");
      const wbout = XLSX.write(wb, { bookType: "xlsx", type: "array" });
      saveAs(
        new Blob([wbout], { type: "application/octet-stream" }),
        "bao_cao.xlsx"
      );
    } else if (type === "pdf") {
      const doc = new jsPDF();
      doc.text("Báo cáo tiến độ dự án", 10, 10);
      let y = 20;
      projectProgress.forEach((item, idx) => {
        doc.text(
          `${idx + 1}. ${item.projectName} (${item.projectCode})`,
          10,
          y
        );
        doc.text(
          `Tiến độ: ${item.percent}% | Thời gian: ${item.time} | Trạng thái: ${item.status}`,
          10,
          y + 8
        );
        y += 18;
        if (y > 270) {
          doc.addPage();
          y = 20;
        }
      });
      y += 10;
      doc.text("Thống kê công việc", 10, y);
      y += 10;
      workStats.forEach((item, idx) => {
        doc.text(`${item.label}: ${item.value}`, 10, y);
        y += 10;
        if (y > 270) {
          doc.addPage();
          y = 20;
        }
      });
      doc.save("bao_cao.pdf");
    }
  };
  return (
    <div className="statistical-list-container">
      <StatisticalCard
        fileIcon={fileIcon}
        chartIcon={chartIcon}
        userGroupIcon={userGroupIcon}
        filterIcon={filterIcon}
        downloadIcon={downloadIcon}
        onFilter={handleFilter}
        onExport={handleExport}
      />
      {loading && projectProgress.length === 0 ? (
        <div style={{ marginTop: 24 }}>
                <div style={{ display: "flex", gap: 32 }}>
                  {/* Skeleton cho báo cáo tiến độ dự án */}
                  <div
                    className="statistical-report-card"
                    style={{
                      background: "#fff",
                      borderRadius: 10,
                      padding: 24,
                      boxShadow: "0 1px 8px #f3f3f3",
                      flex: 1,
                      maxWidth: 800,
                      opacity: 0.7
                    }}
                  >
                    <div
                      style={{
                        width: '200px',
                        height: '26px',
                        background: '#f0f0f0',
                        borderRadius: '4px',
                        marginBottom: '8px',
                        animation: 'pulse 1.5s ease-in-out infinite'
                      }}
                    ></div>
                    <div
                      style={{
                        width: '300px',
                        height: '18px',
                        background: '#f0f0f0',
                        borderRadius: '4px',
                        marginBottom: '24px',
                        animation: 'pulse 1.5s ease-in-out infinite'
                      }}
                    ></div>
                    {[1, 2, 3].map((idx) => (
                      <div key={idx} style={{ marginBottom: 18 }}>
                        <div
                          style={{
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "space-between",
                            marginBottom: 4,
                          }}
                        >
                          <div
                            style={{
                              width: '150px',
                              height: '16px',
                              background: '#f0f0f0',
                              borderRadius: '4px',
                              animation: 'pulse 1.5s ease-in-out infinite'
                            }}
                          ></div>
                          <div
                            style={{
                              width: '40px',
                              height: '16px',
                              background: '#f0f0f0',
                              borderRadius: '4px',
                              animation: 'pulse 1.5s ease-in-out infinite'
                            }}
                          ></div>
                        </div>
                        <div
                          style={{
                            background: "#e5e7eb",
                            borderRadius: 6,
                            height: 7,
                            width: "100%",
                          }}
                        >
                          <div
                            style={{
                              width: `${Math.random() * 100}%`,
                              height: 7,
                              background: '#f0f0f0',
                              borderRadius: 6,
                              animation: 'pulse 1.5s ease-in-out infinite'
                            }}
                          ></div>
                        </div>
                        <div
                          style={{
                            width: '200px',
                            height: '13px',
                            background: '#f0f0f0',
                            borderRadius: '4px',
                            marginTop: 4,
                            animation: 'pulse 1.5s ease-in-out infinite'
                          }}
                        ></div>
                      </div>
                    ))}
                  </div>
                  {/* Skeleton cho thống kê công việc */}
                  <div
                    className="statistical-report-card"
                    style={{
                      background: "#fff",
                      borderRadius: 10,
                      padding: 24,
                      boxShadow: "0 1px 8px #f3f3f3",
                      flex: 1,
                      maxWidth: 800,
                      opacity: 0.7
                    }}
                  >
                    <div
                      style={{
                        width: '180px',
                        height: '26px',
                        background: '#f0f0f0',
                        borderRadius: '4px',
                        marginBottom: '8px',
                        animation: 'pulse 1.5s ease-in-out infinite'
                      }}
                    ></div>
                    <div
                      style={{
                        width: '250px',
                        height: '18px',
                        background: '#f0f0f0',
                        borderRadius: '4px',
                        marginBottom: '24px',
                        animation: 'pulse 1.5s ease-in-out infinite'
                      }}
                    ></div>
                    {[1, 2, 3, 4, 5].map((idx) => (
                      <div
                        key={idx}
                        style={{
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "space-between",
                          marginBottom: 18,
                        }}
                      >
                        <div
                          style={{
                            width: '120px',
                            height: '16px',
                            background: '#f0f0f0',
                            borderRadius: '4px',
                            animation: 'pulse 1.5s ease-in-out infinite'
                          }}
                        ></div>
                        <div
                          style={{
                            width: '40px',
                            height: '20px',
                            background: '#f0f0f0',
                            borderRadius: '12px',
                            animation: 'pulse 1.5s ease-in-out infinite'
                          }}
                        ></div>
                      </div>
                    ))}
                  </div>
                </div>
                {/* CSS Animation */}
                <style>{`
                  @keyframes pulse {
                    0% { opacity: 1; }
                    50% { opacity: 0.5; }
                    100% { opacity: 1; }
                  }
                `}</style>
              </div>
      ) : error ? (
        <div style={{ 
                color: "red", 
                textAlign: "center", 
                padding: "40px",
                marginTop: 24
              }}>
                <div>❌ {error}</div>
                <button 
                  onClick={() => window.location.reload()} 
                  style={{ 
                    marginTop: '10px', 
                    padding: '8px 16px', 
                    background: '#007bff', 
                    color: 'white', 
                    border: 'none', 
                    borderRadius: '4px',
                    cursor: 'pointer'
                  }}
                >
                  Thử lại
                </button>
              </div>
            ) : !loading && projectProgress.length === 0 ? (
              <div style={{ textAlign: 'center', padding: '40px', color: '#666' }}>
                <div>📊 Không có dữ liệu báo cáo.</div>
              </div>
      ) : (
        <div style={{ display: "flex", gap: 32, marginTop: 24 }}>
          {/* Báo cáo tiến độ dự án */}
          <div
            className="statistical-report-card"
            style={{
              background: "#fff",
              borderRadius: 10,
              padding: 24,
              boxShadow: "0 1px 8px #f3f3f3",
              flex: 1,
              maxWidth: 800,
            }}
          >
            <div
              style={{
                fontWeight: 600,
                fontSize: 22,
                color: "#444",
              }}
            >
              Báo cáo tiến độ dự án
            </div>
            <div
              style={{
                color: "#888",
                fontSize: 15,
                marginBottom: 24,
              }}
            >
              Tổng quan tiến độ dự án đang thực hiện
            </div>
            {projectProgress.map((item, idx) => (
              <div key={idx} style={{ marginBottom: 18 }}>
                <div
                  style={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "space-between",
                    fontSize: 15,
                    marginBottom: 4,
                  }}
                >
                  <span>{item.projectName}</span>
                  <span
                    style={{
                      color: "#444",
                      fontWeight: 500,
                    }}
                  >
                    {item.percent}%
                  </span>
                </div>
                <div
                  style={{
                    background: "#e5e7eb",
                    borderRadius: 6,
                    height: 7,
                    width: "100%",
                  }}
                >
                  <div
                    style={{
                      width: `${item.percent}%`,
                      height: 7,
                      background: "#1976ed",
                      borderRadius: 6,
                    }}
                  ></div>
                </div>
                <div
                  style={{
                    color: "#888",
                    fontSize: 13,
                    marginTop: 4,
                  }}
                >
                  Mã dự án: {item.projectCode} | {item.time} |{" "}
                  {item.status}
                </div>
              </div>
            ))}
          </div>
          {/* Thống kê công việc */}
          <div
            className="statistical-report-card"
            style={{
              background: "#fff",
              borderRadius: 10,
              padding: 24,
              boxShadow: "0 1px 8px #f3f3f3",
              flex: 1,
              maxWidth: 800,
            }}
          >
            <div
              style={{
                fontWeight: 600,
                fontSize: 22,
                color: "#444",
              }}
            >
              Thống kê công việc
            </div>
            <div
              style={{
                color: "#888",
                fontSize: 15,
                marginBottom: 24,
              }}
            >
              Phân bổ trạng thái công việc
            </div>
            <div>
              {workStats.map((item, idx) => (
                <div
                  key={idx}
                  style={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "space-between",
                    fontSize: 16,
                    marginBottom: 18,
                  }}
                >
                  <span>{item.label}</span>
                  <span
                    style={{
                      background: "#f3f3f3",
                      borderRadius: 12,
                      padding: "2px 16px",
                      fontWeight: 500,
                    }}
                  >
                    {item.value}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Render;

import { DOCUMENTS_ENDPOINTS } from './endpoints.js';

// Helper function để lấy token từ localStorage
const getAuthToken = () => {
  try {
    const tokenFromStorage = localStorage.getItem('token');
    const authToken = localStorage.getItem('authToken');
    const user = JSON.parse(localStorage.getItem('user') || '{}');
    const userToken = user.token;

    return tokenFromStorage || authToken || userToken;
  } catch (error) {
    console.error('Error getting auth token:', error);
    return null;
  }
};

// Helper function để tạo headers với authorization
const getAuthHeaders = () => {
  const token = getAuthToken();
  return {
    'Content-Type': 'application/json',
    ...(token && { 'Authorization': `Bearer ${token}` })
  };
};

// Helper function để xử lý response
const handleResponse = async (response) => {
  if (!response.ok) {
    let error = { message: 'Có lỗi xảy ra' };
    try {
      error = await response.json();
    } catch {}
    throw new Error(error.message || 'Có lỗi xảy ra');
  }
  return response.json();
};

// ========== DOCUMENTS API FUNCTIONS ==========

// Lấy danh sách tài liệu
export async function getDocuments(params = {}) {
  try {
    const queryString = new URLSearchParams(params).toString();
    const url = `${DOCUMENTS_ENDPOINTS.DOCUMENTS}${queryString ? `?${queryString}` : ''}`;

    const response = await fetch(url, {
      method: 'GET',
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

// Lấy chi tiết tài liệu
export async function getDocument(documentId) {
  try {
    const response = await fetch(DOCUMENTS_ENDPOINTS.DOCUMENT(documentId), {
      method: 'GET',
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

// Tạo tài liệu mới
export async function createDocument(documentData) {
  try {
    const response = await fetch(DOCUMENTS_ENDPOINTS.CREATE_DOCUMENT, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(documentData),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

// Cập nhật tài liệu
export async function updateDocument(documentId, documentData) {
  try {
    const response = await fetch(DOCUMENTS_ENDPOINTS.UPDATE_DOCUMENT(documentId), {
      method: 'PUT',
      headers: getAuthHeaders(),
      body: JSON.stringify(documentData),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

// Xóa tài liệu
export async function deleteDocument(documentId) {
  try {
    const response = await fetch(DOCUMENTS_ENDPOINTS.DELETE_DOCUMENT(documentId), {
      method: 'DELETE',
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

// Upload file cho tài liệu
export async function uploadDocumentFile(documentId, file) {
  try {
    const formData = new FormData();
    formData.append('file', file);

    const token = getAuthToken();
    const response = await fetch(DOCUMENTS_ENDPOINTS.UPLOAD_DOCUMENT_FILE(documentId), {
      method: 'POST',
      headers: {
        ...(token && { 'Authorization': `Bearer ${token}` })
        // Không set Content-Type để browser tự động set với boundary cho FormData
      },
      body: formData,
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

// Download file tài liệu
export async function downloadDocument(documentId) {
  try {
    const token = getAuthToken();
    
    const response = await fetch(DOCUMENTS_ENDPOINTS.DOWNLOAD_DOCUMENT_FILE(documentId), {
      method: 'GET',
      headers: {
        ...(token && { 'Authorization': `Bearer ${token}` })
      },
    });

    if (!response.ok) {
      throw new Error('Không thể tải xuống tài liệu');
    }

    const blob = await response.blob();
    return blob;
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

// Lấy danh sách tài liệu theo dự án
export async function getProjectDocuments(projectId, params = {}) {
  try {
    const queryString = new URLSearchParams(params).toString();
    const url = `${DOCUMENTS_ENDPOINTS.PROJECT_DOCUMENTS(projectId)}${queryString ? `?${queryString}` : ''}`;

    const response = await fetch(url, {
      method: 'GET',
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

// Upload file cho dự án (sử dụng endpoint từ Postman collection)
export async function uploadProjectFile(projectId, file) {
  try {
    const formData = new FormData();
    formData.append('files', file);
    const token = getAuthToken();
    const response = await fetch(DOCUMENTS_ENDPOINTS.UPLOAD_PROJECT_FILE(projectId), {
      method: 'POST',
      headers: {
        ...(token && { 'Authorization': `Bearer ${token}` })
      },
      body: formData,
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

// Lấy danh sách file của dự án
export async function getProjectFiles(projectId) {
  try {
    const response = await fetch(DOCUMENTS_ENDPOINTS.PROJECT_FILES(projectId), {
      method: 'GET',
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

// Xóa file của dự án
export async function deleteProjectFile(projectId, fileId) {
  try {
    const response = await fetch(DOCUMENTS_ENDPOINTS.DELETE_PROJECT_FILE(projectId, fileId), {
      method: 'DELETE',
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

// Tìm kiếm tài liệu
export async function searchDocuments(query, params = {}) {
  try {
    const searchParams = new URLSearchParams({
      query,
      ...params
    });
    
    const response = await fetch(`${DOCUMENTS_ENDPOINTS.DOCUMENTS_SEARCH}?${searchParams}`, {
      method: 'GET',
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
} 

// Hàm xóa tài liệu sử dụng fetch API thuần
export async function deleteDocumentByFetch(documentId) {
  const url = DOCUMENTS_ENDPOINTS.DELETE_DOCUMENT(documentId);
  const token = localStorage.getItem('token');

  const response = await fetch(url, {
    method: 'DELETE',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  });

  if (!response.ok) {
    let error = 'Xóa tài liệu thất bại';
    try {
      const errJson = await response.json();
      error = errJson.message || error;
    } catch {}
    throw new Error(error);
  }
  return response.json();
} 
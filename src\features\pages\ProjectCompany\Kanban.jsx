let kanbanTasksCache = {};
let kanbanCacheTimestamp = {};
const KANBAN_CACHE_DURATION = 10000; // 10 giây
let kanbanPreloadPromiseMap = {};

import React, { useState } from "react";
import { DragDropContext, Droppable, Draggable } from "@hello-pangea/dnd";
import { useOutletContext, useParams } from "react-router-dom";
import "../../../styles/Kanban.css";
import WaitingIcon from "../../../assets/waiting.svg";
import CompleteIcon from "../../../assets/complete.svg";
import ConsiderIcon from "../../../assets/consider.svg";
import OverdueIcon from "../../../assets/triangle-alert.svg";
import Deployment from "../../../assets/deployment.svg";
import CreationDateIcon from "../../../assets/creationdate.svg";
import EndDateIcon from "../../../assets/enddate.svg";
import CommentIcon from "../../../assets/cmt.svg";
import NormalIcon from "../../../assets/Normal.svg";
import MediumIcon from "../../../assets/Medium.svg";
import HighIcon from "../../../assets/High.svg";
import NoJobIcon from "../../../assets/NoJob.svg";
import {
  getProjectTasks,
  updateProjectTask,
  transformTaskListData,
} from "../../../api/taskManagement";

// Mảng cấu hình các cột của bảng Kanban
const columns = [
  {
    key: "Đang chờ",
    title: "Đang chờ",
    // color: "#f59e42",
    // icon: "\u23F3",
  },
  {
    key: "Đang triển khai",
    title: "Đang triển khai",
    // color: "#7c3aed",
    // icon: "\u2728",
  },
  {
    key: "Hoàn thành",
    title: "Hoàn thành",
    // color: "#22c55e",
    // icon: "\u2705",
  },
  {
    key: "Quá hạn",
    title: "Quá hạn",
    // color: "#ff4d4f",
    // icon: "\u26A0",
  },
  {
    key: "Xem xét",
    title: "Xem xét",
    // color: "#ef4444",
    // icon: "\u2B24",
  },
];

// Đối tượng ánh xạ trạng thái sang icon
const statusIcons = {
  "Đang chờ": WaitingIcon,
  "Đang triển khai": Deployment,
  "Hoàn thành": CompleteIcon,
  "Quá hạn": OverdueIcon,
  "Xem xét": ConsiderIcon,
};
// Đối tượng ánh xạ mức độ ưu tiên sang icon
const priorityIcons = {
  "Thấp": NormalIcon,
  "Trung Bình": MediumIcon,
  "Cao": HighIcon,
};

// Thêm ánh xạ status backend -> key Kanban
const statusMap = {
  pending: "Đang chờ",
  in_progress: "Đang triển khai",
  completed: "Hoàn thành",
  overdue: "Quá hạn",
  review: "Xem xét",
};

// Thêm ánh xạ priority backend -> key Kanban
const priorityMap = {
  low: "Thấp",
  medium: "Trung Bình",
  high: "Cao"
};

// Map ngược tên cột Kanban sang status backend
const kanbanColKeyToStatus = {
  "Đang chờ": "pending",
  "Đang triển khai": "in_progress",
  "Hoàn thành": "completed",
  "Quá hạn": "overdue",
  "Xem xét": "review",
};

// Thêm hàm flattenTasks để chuyển danh sách task cha/con thành mảng phẳng
function flattenTasks(tasks) {
  let flat = [];
  tasks.forEach((task) => {
    flat.push(task);
    if (task.children && task.children.length > 0) {
      flat = flat.concat(flattenTasks(task.children));
    }
  });
  return flat;
}

function Kanban() {
  const { projectId } = useParams();
  // State lưu danh sách công việc
  const [tasks, setTasks] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Lấy context từ component cha (WorkContent) nếu có
  const context = useOutletContext() || {};
  const { sortBy: sortOption, filters: filterOptions } = context;

  // TODO: Áp dụng sắp xếp và lọc cho tasks nếu cần

  // Preload function
  const preloadKanbanTasks = async (projectId) => {
    if (kanbanPreloadPromiseMap[projectId]) return kanbanPreloadPromiseMap[projectId];
    kanbanPreloadPromiseMap[projectId] = (async () => {
      try {
        const data = await getProjectTasks(projectId);
        const tasks = transformTaskListData(data.data || data);
        const flat = flattenTasks(tasks);
        kanbanTasksCache[projectId] = flat;
        kanbanCacheTimestamp[projectId] = Date.now();
        return flat;
      } catch {
        return [];
      }
    })();
    return kanbanPreloadPromiseMap[projectId];
  };

  // Khi projectId thay đổi, load lại task list tương ứng
  React.useEffect(() => {
    const now = Date.now();
    if (kanbanTasksCache[projectId] && (now - kanbanCacheTimestamp[projectId]) < KANBAN_CACHE_DURATION) {
      setTasks(kanbanTasksCache[projectId]);
      setLoading(false);
      return;
    }
    setLoading(true);
    let flat = [];
    if (kanbanPreloadPromiseMap[projectId]) {
      kanbanPreloadPromiseMap[projectId].then((data) => {
        setTasks(data);
        setLoading(false);
      });
    } else {
      getProjectTasks(projectId)
        .then((data) => {
          const tasks = transformTaskListData(data.data || data);
          flat = flattenTasks(tasks);
          setTasks(flat);
          kanbanTasksCache[projectId] = flat;
          kanbanCacheTimestamp[projectId] = Date.now();
          setLoading(false);
        })
        .catch((err) => {
          setError(err.message || "Lỗi khi tải công việc");
          setLoading(false);
        });
    }
    // Lắng nghe sự kiện đồng bộ từ ListProject hoặc nơi khác
    const reloadHandler = () => {
      setLoading(true);
      setError(null);
      getProjectTasks(projectId)
        .then((data) => {
          const tasks = transformTaskListData(data.data || data);
          const flat = flattenTasks(tasks);
          setTasks(flat);
          kanbanTasksCache[projectId] = flat;
          kanbanCacheTimestamp[projectId] = Date.now();
          setLoading(false);
        })
        .catch((err) => {
          setError(err.message || "Lỗi khi tải công việc");
          setLoading(false);
        });
    };
    window.addEventListener("projectTasksUpdated", reloadHandler);
    return () =>
      window.removeEventListener("projectTasksUpdated", reloadHandler);
  }, [projectId]);

  React.useEffect(() => { if (projectId) preloadKanbanTasks(projectId); }, [projectId]);

  // Hàm xử lý khi kéo thả task giữa các cột
  const onDragEnd = async (result) => {
    if (!result.destination) return;
    const { source, destination, draggableId } = result;
    if (
      source.droppableId === destination.droppableId &&
      source.index === destination.index
    )
      return;

    const movedTaskIndex = tasks.findIndex(
      (t) => t.id + t.status === draggableId
    );
    if (movedTaskIndex === -1) return;

    // Lưu trạng thái cũ để revert nếu cần
    const oldStatus = tasks[movedTaskIndex].status;
    const newStatus = kanbanColKeyToStatus[destination.droppableId];

    // Optimistic update: cập nhật state ngay
    const updatedTasks = [...tasks];
    updatedTasks[movedTaskIndex] = {
      ...updatedTasks[movedTaskIndex],
      status: newStatus,
    };
    setTasks(updatedTasks);

    try {
      await updateProjectTask(projectId, tasks[movedTaskIndex].id, {
        status: newStatus,
      });
      // Không cần fetch lại toàn bộ, chỉ cần đồng bộ các nơi khác nếu có
      window.dispatchEvent(new Event("projectTasksUpdated"));
    } catch (err) {
      // Nếu lỗi, revert lại trạng thái cũ
      updatedTasks[movedTaskIndex] = {
        ...updatedTasks[movedTaskIndex],
        status: oldStatus,
      };
      setTasks(updatedTasks);
      alert(
        "Cập nhật trạng thái thất bại: " + (err.message || "Unknown error")
      );
    }
  };

  return (
    <DragDropContext onDragEnd={onDragEnd}>
      <div className="kanban-board">
        {loading ? (
          columns.map((col) => (
            <div className="kanban-column" key={col.key}>
              <div className="kanban-column-header">
                <img
                  src={statusIcons[col.title]}
                  alt="icon"
                  style={{ width: 16, height: 16, marginRight: 4 }}
                />
                <span className="kanban-column-title">{col.title}</span>
                <span className="kanban-column-count">(0 công việc)</span>
              </div>
              <div className="kanban-tasks">
                {[1,2,3].map(i => (
                  <div key={i} className="kanban-task" style={{ opacity: 0.7 }}>
                    <div className="kanban-task-header">
                      <div style={{ width: 80, height: 16, background: '#f0f0f0', borderRadius: 4, marginBottom: 6, animation: 'pulse 1.5s ease-in-out infinite' }}></div>
                    </div>
                    <div className="kanban-task-priority">
                      <div style={{ width: 60, height: 14, background: '#f0f0f0', borderRadius: 4, animation: 'pulse 1.5s ease-in-out infinite' }}></div>
                    </div>
                    <div className="kanban-task-dates">
                      <div className="kanban-task-dates-row">
                        <div style={{ width: 80, height: 12, background: '#f0f0f0', borderRadius: 4, animation: 'pulse 1.5s ease-in-out infinite' }}></div>
                      </div>
                      <div className="kanban-task-dates-row">
                        <div style={{ width: 80, height: 12, background: '#f0f0f0', borderRadius: 4, animation: 'pulse 1.5s ease-in-out infinite' }}></div>
                      </div>
                    </div>
                    <div className="kanban-task-footer">
                      <div className="kanban-task-members">
                        <div style={{ width: 60, height: 14, background: '#f0f0f0', borderRadius: 4, animation: 'pulse 1.5s ease-in-out infinite' }}></div>
                      </div>
                      <div className="kanban-task-comments">
                        <div style={{ width: 40, height: 14, background: '#f0f0f0', borderRadius: 4, animation: 'pulse 1.5s ease-in-out infinite' }}></div>
                      </div>
                    </div>
                  </div>
                ))}
                <style>{`
                  @keyframes pulse {
                    0% { opacity: 1; }
                    50% { opacity: 0.5; }
                    100% { opacity: 1; }
                  }
                `}</style>
              </div>
            </div>
          ))
        ) : error ? (
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              height: 350,
              color: "red",
            }}
          >
            {error}
          </div>
        ) : tasks.length === 0 ? (
          <div
            style={{
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              justifyContent: "center",
              height: "60vh",
              color: "#ccc",
              width: "100%"
            }}
          >
            <img
              src={NoJobIcon}
              alt="Công việc trống"
              style={{ width: 600, marginBottom: 16 }}
            />
          </div>
        ) : (
          columns.map((col) => {
            const colTasks = tasks.filter(
              (t) => statusMap[t.status] === col.key
            );
            return (
              <Droppable droppableId={col.key} key={col.key}>
                {(provided) => (
                  <div
                    className="kanban-column"
                    ref={provided.innerRef}
                    {...provided.droppableProps}
                  >
                    <div className="kanban-column-header">
                      <img
                        src={statusIcons[col.title]}
                        alt="icon"
                        style={{ width: 16, height: 16, marginRight: 4 }}
                      />
                      <span className="kanban-column-title">{col.title}</span>
                      <span className="kanban-column-count">
                        ({colTasks.length} công việc)
                      </span>
                    </div>
                    <div className="kanban-tasks">
                      {colTasks.map((task, idx) => (
                        <Draggable
                          draggableId={task.id + task.status}
                          index={idx}
                          key={task.id + task.status}
                        >
                          {(provided, snapshot) => (
                            <div
                              className="kanban-task"
                              ref={provided.innerRef}
                              {...provided.draggableProps}
                              {...provided.dragHandleProps}
                              style={{
                                ...provided.draggableProps.style,
                                boxShadow: snapshot.isDragging
                                  ? "0 4px 16px rgba(0,0,0,0.15)"
                                  : undefined,
                              }}
                            >
                              <div className="kanban-task-header">
                                <span className="kanban-task-title">
                                  {task.name}
                                </span>
                              </div>
                              <div className="kanban-task-priority">
                                {(() => {
                                  const priorityLabel = priorityMap[task.priority] || "Thấp";
                                  return <>
                                    <img
                                      src={priorityIcons[priorityLabel]}
                                      alt="icon"
                                      style={{ width: 16, marginRight: 4 }}
                                    />
                                    <span>{priorityLabel}</span>
                                  </>;
                                })()}
                              </div>
                              <div className="kanban-task-dates">
                                <div className="kanban-task-dates-row">
                                  <span className="kanban-task-dates-label">
                                    Ngày bắt đầu
                                  </span>
                                  <img
                                    src={CreationDateIcon}
                                    alt="ngày tạo"
                                    className="kanban-task-dates-icon"
                                  />
                                  <span className="kanban-task-dates-value">
                                    {task.startDate}
                                  </span>
                                </div>
                                <div className="kanban-task-dates-row">
                                  <span className="kanban-task-dates-label">
                                    Ngày kết thúc
                                  </span>
                                  <img
                                    src={EndDateIcon}
                                    alt="ngày kết thúc"
                                    className="kanban-task-dates-icon"
                                  />
                                  <span className="kanban-task-dates-value">
                                    {task.dueDate}
                                  </span>
                                </div>
                              </div>
                              <div className="kanban-task-footer">
                                <div className="kanban-task-members">
                                  Thành viên:{" "}
                                  {task.assignee && task.assignee.length > 0 ? (
                                    task.assignee.map((member, i) => (
                                      <span key={i} style={{ marginLeft: "4px" }}>
                                        {member.name}
                                        {i < task.assignee.length - 1 ? ", " : ""}
                                      </span>
                                    ))
                                  ) : (
                                    <span style={{ color: "#999", fontStyle: "italic" }}>
                                      Chưa giao việc
                                    </span>
                                  )}
                                </div>
                                <div className="kanban-task-comments">
                                  <img
                                    src={CommentIcon}
                                    alt="cmt"
                                    style={{ width: 16, marginRight: 4 }}
                                  />{" "}
                                  {task.comments || 0}
                                </div>
                              </div>
                            </div>
                          )}
                        </Draggable>
                      ))}
                      {provided.placeholder}
                    </div>
                  </div>
                )}
              </Droppable>
            );
          })
        )}
      </div>
    </DragDropContext>
  );
}

export default Kanban;

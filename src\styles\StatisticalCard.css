@import url('../index.css');
.statistical-cards-wrapper {
  width: 100%;
}
.statistical-list-title{
  font-size: 22px;
  font-weight: 600;
  color: #5D5D5D;
  padding: 0px 0px 20px 0px;
}
.statistical-cards {
  display: flex;
  gap: 24px;
}
.stat-card {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.03);
  padding: 24px 32px 16px 24px;
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  min-width: 180px;
}
.stat-title {
  font-size: 15px;
  color: #888;
  margin-bottom: 8px;
}
.stat-title.stat-title-flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  gap: 8px;
}
.stat-value {
  font-size: 28px;
  font-weight: 700;
  color: #222;
}
.stat-desc {
  font-size: 13px;
  color: #bdbdbd;
  margin-top: 4px;
}
.statistical-header-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 24px 0;
}
.tabs-list {
  display: flex;
  gap: 4px;
  background: #ECEEF080;
  border-radius: 12px;
  padding: 6px;
  width: fit-content;
}
.tabs-trigger {
  background: transparent;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 15px;
  color: #666;
  cursor: pointer;
  font-weight: 500;
  transition: background 0.2s, color 0.2s;
  white-space: nowrap;
}
.tabs-trigger.active {
  background: #fff;
  color: #333;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}
.tabs-trigger:hover:not(.active) {
  background: rgba(255,255,255,0.5);
  color: #444;
}
.btn-filter,
.btn-export {
  display: flex;
  align-items: center;
  gap: 4px;
  background: #fff;
  border: 1.5px solid #e5e7eb;
  border-radius: 8px;
  padding: 6px 16px;
  font-size: 15px;
  color: #222;
  cursor: pointer;
  transition: background 0.15s, border 0.15s;
  height: 36px;
  margin: 0;
}
.btn-filter:hover,
.btn-export:hover {
  background: #f7f8fa;
  border-color: #bfc5ce;
}
.statistical-actions {
  display: flex;
  gap: 12px;
  margin-bottom: 0;
  justify-content: flex-end;
}
